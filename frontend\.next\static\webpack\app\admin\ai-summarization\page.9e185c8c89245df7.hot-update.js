"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/ai-summarization/page",{

/***/ "(app-pages-browser)/./src/app/admin/AiSummarizationPanel.tsx":
/*!************************************************!*\
  !*** ./src/app/admin/AiSummarizationPanel.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AiSummarizationPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _barrel_optimize_names_Check_CheckCheck_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CheckCheck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_CheckCheck_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CheckCheck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-check.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction PageList(param) {\n    let { pages, selected, onSelect } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full mb-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \" font-semibold text-brand-blue/[85%] mt-2 mb-4\",\n                children: [\n                    \"Medical Course Pages (\",\n                    pages.length,\n                    \" pages)\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                className: \"h-full space-y-3 overflow-y-auto max-h-[calc(100vh-5%)] pr-2 custom-scrollbar\",\n                children: pages.map((p, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"w-full text-left px-5 py-3 rounded-xl border transition-all duration-200 flex flex-col gap-1 shadow-sm \\n                \".concat(selected === idx ? \"border-orange-500 bg-orange-50 text-orange-800 shadow-md\" : \"border-gray-200 bg-white hover:bg-orange-50 hover:border-orange-300\", \"\\n              \"),\n                            onClick: ()=>onSelect(idx),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-bold text-lg\",\n                                    children: [\n                                        \"Page \",\n                                        p.page\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600 truncate\",\n                                    children: p.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"inline-block mt-1 text-xs px-3 py-1 rounded-full font-medium \\n                \".concat(p.status === \"Approved\" ? \"bg-green-100 text-green-700\" : \"bg-orange-100 text-orange-600\", \"\\n              \"),\n                                    children: p.status\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 13\n                        }, this)\n                    }, p.page, false, {\n                        fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n_c = PageList;\nfunction SummaryPanel(param) {\n    let { page, onChangeSummary, onApprove, onRegenerate, onCustomPrompt, onViewOriginal, onApproveAll, approvedCount, totalCount, pages, selected, onSelect } = param;\n    const progressPercentage = totalCount > 0 ? approvedCount / totalCount * 100 : 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 flex flex-col bg-white rounded-2xl shadow-xl px-4 py-4 border border-orange-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-brand-blue flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-7 h-7 text-orange-500\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: \"2\",\n                                    d: \"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-brand-blue\",\n                                children: \"AI-Powered Medical Summarization\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-md font-medium text-brand-blue-300\",\n                        children: [\n                            \"Progress: \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-orange-600\",\n                                children: approvedCount\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 76\n                            }, this),\n                            \" of \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-brand-blue-300\",\n                                children: totalCount\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 136\n                            }, this),\n                            \" approved\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full bg-brand-blue-300/[20%] rounded-full h-2.5 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-green-500 h-2.5 rounded-full transition-all duration-500 ease-out\",\n                    style: {\n                        width: \"\".concat(progressPercentage, \"%\")\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-3 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-bold text-brand-blue-300\",\n                                            children: page.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 flex justify-end gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.Tooltip, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipTrigger, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-100 text-sm font-medium transition flex items-center gap-1 shadow-sm\",\n                                                                onClick: onRegenerate,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 text-gray-600\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"2\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M4 4v5h.582M20 20v-5h-.581\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                            lineNumber: 68,\n                                                                            columnNumber: 130\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M5.077 19A9 9 0 1 0 6.5 6.5L4 9.5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                            lineNumber: 68,\n                                                                            columnNumber: 168\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                    lineNumber: 68,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                lineNumber: 67,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                            lineNumber: 66,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipContent, {\n                                                            variant: \"brand\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"Regenerate\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                lineNumber: 72,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                            lineNumber: 71,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                    lineNumber: 65,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.Tooltip, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipTrigger, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-100 text-sm font-medium transition flex items-center gap-1 shadow-sm\",\n                                                                onClick: onCustomPrompt,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 text-gray-600\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"2\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M16.862 3.487a2.6 2.6 0 1 1 3.677 3.677L7.5 20.205l-4.5 1 1-4.5L16.862 3.487Z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                            lineNumber: 78,\n                                                                            columnNumber: 126\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M15 6.5 17.5 9\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                            lineNumber: 78,\n                                                                            columnNumber: 215\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                    lineNumber: 78,\n                                                                    columnNumber: 17\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                lineNumber: 77,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                            lineNumber: 76,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipContent, {\n                                                            variant: \"brand\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"Custom Prompt\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                lineNumber: 82,\n                                                                columnNumber: 20\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                            lineNumber: 81,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.Tooltip, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipTrigger, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-100 text-sm font-medium transition flex items-center gap-1 shadow-sm\",\n                                                                onClick: onViewOriginal,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 text-gray-600\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"2\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M1.5 12S5 5 12 5s10.5 7 10.5 7-3.5 7-10.5 7S1.5 12 1.5 12Z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                            lineNumber: 88,\n                                                                            columnNumber: 126\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                            cx: \"12\",\n                                                                            cy: \"12\",\n                                                                            r: \"3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                            lineNumber: 88,\n                                                                            columnNumber: 196\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                    lineNumber: 88,\n                                                                    columnNumber: 17\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                lineNumber: 87,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                            lineNumber: 86,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipContent, {\n                                                            variant: \"brand\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"View Original\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                lineNumber: 92,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                            lineNumber: 91,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                    lineNumber: 85,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 flex-1 flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"AI-Generated Summary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            className: \"w-full border border-gray-200 rounded-lg px-4 py-3 min-h-[150px] focus:ring-2 focus:ring-orange-200 focus:border-orange-300 transition-colors duration-200 resize-y flex-1\",\n                                            value: page.aiSummary,\n                                            onChange: (e)=>onChangeSummary(e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mt-4 text-xs text-gray-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-orange-50 text-orange-600 px-2 py-1 rounded-full font-medium\",\n                                                    children: \"Generated by Azure OpenAI GPT-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-brand-blue/[25%] text-brand-blue px-2 py-1 rounded-full font-medium\",\n                                                    children: \"Medical AI Model\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 flex-1 flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Original Content Preview\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 text-gray-700 text-sm min-h-[150px] overflow-y-auto flex-1 custom-scrollbar\",\n                                            children: page.original\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-3 mt-2 justify-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            variant: 'outline',\n                                            className: \"text-brand-blue border-brand-blue hover:bg-brand-blue hover:text-white px-6 py-3 rounded-lg font-semibold shadow-md hover:shadow-lg transition-all duration-200 flex items-center gap-2\",\n                                            onClick: onApprove,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCheck_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 15\n                                                }, this),\n                                                \"Approve\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            variant: 'outline',\n                                            className: \"text-brand-blue border-brand-blue hover:bg-brand-blue hover:text-white px-6 py-3 rounded-lg font-semibold shadow-md hover:shadow-lg transition-all duration-200 flex items-center gap-2\",\n                                            onClick: onApproveAll,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCheck_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 15\n                                                }, this),\n                                                \"Approve All (\",\n                                                pages.filter((p)=>p.status === \"Pending Review\").length,\n                                                \" remaining)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PageList, {\n                            pages: pages,\n                            selected: selected,\n                            onSelect: onSelect\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n_c1 = SummaryPanel;\nfunction AiSummarizationPanel() {\n    _s();\n    const [selected, setSelected] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(0);\n    const [pages, setPages] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [showPromptModal, setShowPromptModal] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [customPrompt, setCustomPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [courses, setCourses] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [selectedCourseId, setSelectedCourseId] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [showApproveAllModal, setShowApproveAllModal] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [selectedCourseBlobUrl, setSelectedCourseBlobUrl] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"AiSummarizationPanel.useEffect\": ()=>{\n            // Fetch courses on mount\n            fetch(\"http://172.24.175.70:5001\" + \"/api/courses\").then({\n                \"AiSummarizationPanel.useEffect\": (res)=>res.json()\n            }[\"AiSummarizationPanel.useEffect\"]).then({\n                \"AiSummarizationPanel.useEffect\": (data)=>setCourses(data)\n            }[\"AiSummarizationPanel.useEffect\"]);\n        }\n    }[\"AiSummarizationPanel.useEffect\"], []);\n    const handleCourseSelect = async (value)=>{\n        const courseId = value;\n        setSelectedCourseId(courseId);\n        setLoading(true);\n        const res = await fetch(\"http://172.24.175.70:5001\" + '/api/courses/' + courseId);\n        const data = await res.json();\n        // Map pdf_output to the format expected by the UI\n        const mappedPages = (data.pdf_output || []).map((p, idx)=>({\n                page: p.page || idx + 1,\n                title: \"Page \".concat(p.page || idx + 1),\n                status: \"Pending Review\",\n                aiSummary: p.imagetext || \"\",\n                original: p.imagetext || \"\",\n                imageassestid: p.imageassestid\n            }));\n        setPages(mappedPages);\n        setSelected(0);\n        setLoading(false);\n        setSelectedCourseBlobUrl(data.blob_url || null);\n    };\n    const handleChangeSummary = (val)=>{\n        setPages((pages)=>pages.map((p, idx)=>idx === selected ? {\n                    ...p,\n                    aiSummary: val\n                } : p));\n    };\n    const handleApprove = ()=>{\n        setPages((pages)=>pages.map((p, idx)=>idx === selected ? {\n                    ...p,\n                    status: \"Approved\"\n                } : p));\n    };\n    const handleApproveAll = ()=>{\n        setPages((pages)=>pages.map((p)=>({\n                    ...p,\n                    status: \"Approved\"\n                })));\n    };\n    const handleRegenerate = async ()=>{\n        const text = pages[selected].aiSummary;\n        try {\n            const res = await fetch(\"\".concat(\"http://172.24.175.70:5002\", \"/api/rephrase\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    text\n                })\n            });\n            const data = await res.json();\n            if (data && data.rephrased) {\n                setPages((pages)=>pages.map((p, idx)=>idx === selected ? {\n                            ...p,\n                            aiSummary: data.rephrased\n                        } : p));\n            } else if (typeof data === 'string') {\n                setPages((pages)=>pages.map((p, idx)=>idx === selected ? {\n                            ...p,\n                            aiSummary: data\n                        } : p));\n            }\n        } catch (err) {\n            alert('Failed to rephrase.');\n        }\n    };\n    const handleCustomPrompt = ()=>{\n        setShowPromptModal(true);\n    };\n    const handleViewOriginal = ()=>{\n        if (selectedCourseBlobUrl) {\n            window.open(selectedCourseBlobUrl, '_blank');\n        } else {\n            alert(\"No original content URL available.\");\n        }\n    };\n    const handlePromptCancel = ()=>{\n        setShowPromptModal(false);\n        setCustomPrompt(\"\");\n    };\n    const handlePromptGenerate = async ()=>{\n        const summaryText = pages[selected].aiSummary;\n        const promptText = customPrompt;\n        try {\n            const res = await fetch(\"\".concat(\"http://172.24.175.70:5002\", \"/api/customrephrase\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    text: summaryText,\n                    prompt: promptText\n                })\n            });\n            const data = await res.json();\n            if (data && data.result) {\n                setPages((pages)=>pages.map((p, idx)=>idx === selected ? {\n                            ...p,\n                            aiSummary: data.result\n                        } : p));\n            } else {\n                alert('Failed to get rephrased result.');\n            }\n        } catch (err) {\n            alert('Failed to get rephrased result.');\n        }\n        setShowPromptModal(false);\n        setCustomPrompt(\"\");\n    };\n    const handleApproveAllClick = ()=>{\n        setShowApproveAllModal(true);\n    };\n    const handleApproveAllConfirm = async ()=>{\n        setShowApproveAllModal(false);\n        handleApproveAll();\n        // Get user email from localStorage token\n        let userEmail = \"unknown\";\n        try {\n            const token = localStorage.getItem(\"token\");\n            if (token) {\n                const userStr = atob(token.split(\".\")[1]);\n                const user = JSON.parse(userStr);\n                userEmail = user.email || \"unknown\";\n            }\n        } catch (e) {}\n        // Send approved data to backend\n        try {\n            const res = await fetch(\"\".concat(\"http://172.24.175.70:5001\", \"/api/courses/\").concat(selectedCourseId, \"/approve\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    content: pages.map((p)=>({\n                            ...p,\n                            status: \"Approved\"\n                        })),\n                    approved_by: userEmail,\n                    comment: ''\n                })\n            });\n            if (!res.ok) {\n                alert('Failed to save approved content!');\n            } else {\n                setSuccessMessage('Content approved and saved!');\n                setTimeout(()=>setSuccessMessage(\"\"), 3000);\n            }\n        } catch (err) {\n            alert('Failed to save approved content!');\n        }\n    };\n    const handleApproveAllCancel = ()=>{\n        setShowApproveAllModal(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full px-4 py-4 flex flex-col items-stretch justify-start relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                className: \"flex gap-4 items-end py-4\",\n                onSubmit: (e)=>e.preventDefault(),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_2__.Label, {\n                            htmlFor: \"course\",\n                            className: \"py-2\",\n                            children: \"Course\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.Select, {\n                            value: selectedCourseId || '',\n                            onValueChange: (value)=>handleCourseSelect(value),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectTrigger, {\n                                    className: \"w-auto focus:ring-2 focus:ring-orange-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectValue, {\n                                        placeholder: \"Select Course\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectContent, {\n                                    children: courses.map((c)=>// <option key={c.id} value={c.id}>{c.title} ({c.domain})</option>\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                            value: c.id.toString(),\n                                            children: [\n                                                c.title,\n                                                \" (\",\n                                                c.domain,\n                                                \")\"\n                                            ]\n                                        }, c.id, true, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                    lineNumber: 296,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 295,\n                columnNumber: 9\n            }, this),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-lg text-orange-500\",\n                children: \"Loading course data...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 326,\n                columnNumber: 19\n            }, this),\n            !loading && pages.length > 0 && // <div className=\"w-full bg-white rounded-2xl shadow-lg p-8 mt-8\">\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SummaryPanel, {\n                page: pages[selected],\n                onChangeSummary: handleChangeSummary,\n                onApprove: handleApprove,\n                onRegenerate: handleRegenerate,\n                onCustomPrompt: handleCustomPrompt,\n                onViewOriginal: handleViewOriginal,\n                onApproveAll: handleApproveAllClick,\n                approvedCount: pages.filter((p)=>p.status === \"Approved\").length,\n                totalCount: pages.length,\n                pages: pages,\n                selected: selected,\n                onSelect: setSelected\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 329,\n                columnNumber: 11\n            }, this),\n            showPromptModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full animate-fade-in-up relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-bold text-gray-800 mb-4\",\n                            children: \"Custom AI Prompt\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            className: \"w-full border border-gray-300 rounded-lg p-3 min-h-[120px] focus:ring-2 focus:ring-orange-200 focus:border-orange-300 transition-colors duration-200 resize-y\",\n                            placeholder: \"Enter your custom prompt here to regenerate the summary...\",\n                            value: customPrompt,\n                            onChange: (e)=>setCustomPrompt(e.target.value)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-3 rounded-lg font-semibold transition-colors duration-200\",\n                                    onClick: handlePromptCancel,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200\",\n                                    onClick: handlePromptGenerate,\n                                    children: \"Generate\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 357,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                    lineNumber: 349,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 348,\n                columnNumber: 9\n            }, this),\n            showApproveAllModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-2xl p-8 max-w-sm w-full animate-fade-in-up relative text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-bold text-gray-800 mb-4\",\n                            children: \"Confirm Approval\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-6\",\n                            children: \"Are you sure you want to approve all pending summaries for this course? This action cannot be undone.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 380,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-3 rounded-lg font-semibold transition-colors duration-200\",\n                                    onClick: handleApproveAllCancel,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200\",\n                                    onClick: handleApproveAllConfirm,\n                                    children: \"Confirm Approve All\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 381,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                    lineNumber: 378,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 377,\n                columnNumber: 9\n            }, this),\n            successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-6 right-6 bg-green-500 text-white px-6 py-3 rounded-lg shadow-xl text-lg font-semibold animate-fade-in-right z-50\",\n                children: successMessage\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 400,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n        lineNumber: 292,\n        columnNumber: 5\n    }, this);\n}\n_s(AiSummarizationPanel, \"CO/z/i0KmRkbIZmMS/BiU85F0sQ=\");\n_c2 = AiSummarizationPanel;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"PageList\");\n$RefreshReg$(_c1, \"SummaryPanel\");\n$RefreshReg$(_c2, \"AiSummarizationPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/AiSummarizationPanel.tsx\n"));

/***/ })

});