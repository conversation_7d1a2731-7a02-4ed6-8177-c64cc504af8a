"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("main-app",{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/app-find-source-map-url.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/client/app-find-source-map-url.js ***!
  \******************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"findSourceMapURL\", ({\n    enumerable: true,\n    get: function() {\n        return findSourceMapURL;\n    }\n}));\nconst basePath =  false || '';\nconst pathname = \"\" + basePath + \"/__nextjs_source-map\";\nconst findSourceMapURL =  true ? function findSourceMapURL(filename) {\n    if (filename === '') {\n        return null;\n    }\n    if (filename.startsWith(document.location.origin) && filename.includes('/_next/static')) {\n        // This is a request for a client chunk. This can only happen when\n        // using Turbopack. In this case, since we control how those source\n        // maps are generated, we can safely assume that the sourceMappingURL\n        // is relative to the filename, with an added `.map` extension. The\n        // browser can just request this file, and it gets served through the\n        // normal dev server, without the need to route this through\n        // the `/__nextjs_source-map` dev middleware.\n        return \"\" + filename + \".map\";\n    }\n    const url = new URL(pathname, document.location.origin);\n    url.searchParams.set('filename', filename);\n    return url.href;\n} : 0;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-find-source-map-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-find-source-map-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/app-link-gc.js":
/*!******************************************************!*\
  !*** ./node_modules/next/dist/client/app-link-gc.js ***!
  \******************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"linkGc\", ({\n    enumerable: true,\n    get: function() {\n        return linkGc;\n    }\n}));\nfunction linkGc() {\n    // TODO-APP: Remove this logic when Float has GC built-in in development.\n    if (true) {\n        const callback = (mutationList)=>{\n            for (const mutation of mutationList){\n                if (mutation.type === 'childList') {\n                    for (const node of mutation.addedNodes){\n                        if ('tagName' in node && node.tagName === 'LINK') {\n                            var _link_dataset_precedence;\n                            const link = node;\n                            if ((_link_dataset_precedence = link.dataset.precedence) == null ? void 0 : _link_dataset_precedence.startsWith('next')) {\n                                const href = link.getAttribute('href');\n                                if (href) {\n                                    const [resource, version] = href.split('?v=', 2);\n                                    if (version) {\n                                        const currentOrigin = window.location.origin;\n                                        const allLinks = [\n                                            ...document.querySelectorAll('link[href^=\"' + resource + '\"]'),\n                                            // It's possible that the resource is a full URL or only pathname,\n                                            // so we need to remove the alternative href as well.\n                                            ...document.querySelectorAll('link[href^=\"' + (resource.startsWith(currentOrigin) ? resource.slice(currentOrigin.length) : currentOrigin + resource) + '\"]')\n                                        ];\n                                        for (const otherLink of allLinks){\n                                            var _otherLink_dataset_precedence;\n                                            if ((_otherLink_dataset_precedence = otherLink.dataset.precedence) == null ? void 0 : _otherLink_dataset_precedence.startsWith('next')) {\n                                                const otherHref = otherLink.getAttribute('href');\n                                                if (otherHref) {\n                                                    const [, otherVersion] = otherHref.split('?v=', 2);\n                                                    if (!otherVersion || +otherVersion < +version) {\n                                                        // Delay the removal of the stylesheet to avoid FOUC\n                                                        // caused by `@font-face` rules, as they seem to be\n                                                        // a couple of ticks delayed between the old and new\n                                                        // styles being swapped even if the font is cached.\n                                                        setTimeout(()=>{\n                                                            otherLink.remove();\n                                                        }, 5);\n                                                        const preloadLink = document.querySelector('link[rel=\"preload\"][as=\"style\"][href=\"' + otherHref + '\"]');\n                                                        if (preloadLink) {\n                                                            preloadLink.remove();\n                                                        }\n                                                    }\n                                                }\n                                            }\n                                        }\n                                    }\n                                }\n                            }\n                        }\n                    }\n                }\n            }\n        };\n        // Create an observer instance linked to the callback function\n        const observer = new MutationObserver(callback);\n        observer.observe(document.head, {\n            childList: true\n        });\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-link-gc.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-link-gc.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/app-router-instance.js ***!
  \*************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createMutableActionQueue: function() {\n        return createMutableActionQueue;\n    },\n    dispatchNavigateAction: function() {\n        return dispatchNavigateAction;\n    },\n    dispatchTraverseAction: function() {\n        return dispatchTraverseAction;\n    },\n    getCurrentAppRouterState: function() {\n        return getCurrentAppRouterState;\n    },\n    publicAppRouterInstance: function() {\n        return publicAppRouterInstance;\n    }\n});\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _routerreducer = __webpack_require__(/*! ./router-reducer/router-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _isthenable = __webpack_require__(/*! ../../shared/lib/is-thenable */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/is-thenable.js\");\nconst _segmentcache = __webpack_require__(/*! ./segment-cache */ \"(app-pages-browser)/./node_modules/next/dist/client/components/segment-cache.js\");\nconst _useactionqueue = __webpack_require__(/*! ./use-action-queue */ \"(app-pages-browser)/./node_modules/next/dist/client/components/use-action-queue.js\");\nconst _addbasepath = __webpack_require__(/*! ../add-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/add-base-path.js\");\nconst _approuter = __webpack_require__(/*! ./app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\");\nconst _prefetchreducer = __webpack_require__(/*! ./router-reducer/reducers/prefetch-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/prefetch-reducer.js\");\nconst _links = __webpack_require__(/*! ./links */ \"(app-pages-browser)/./node_modules/next/dist/client/components/links.js\");\nfunction runRemainingActions(actionQueue, setState) {\n    if (actionQueue.pending !== null) {\n        actionQueue.pending = actionQueue.pending.next;\n        if (actionQueue.pending !== null) {\n            // eslint-disable-next-line @typescript-eslint/no-use-before-define\n            runAction({\n                actionQueue,\n                action: actionQueue.pending,\n                setState\n            });\n        } else {\n            // No more actions are pending, check if a refresh is needed\n            if (actionQueue.needsRefresh) {\n                actionQueue.needsRefresh = false;\n                actionQueue.dispatch({\n                    type: _routerreducertypes.ACTION_REFRESH,\n                    origin: window.location.origin\n                }, setState);\n            }\n        }\n    }\n}\nasync function runAction(param) {\n    let { actionQueue, action, setState } = param;\n    const prevState = actionQueue.state;\n    actionQueue.pending = action;\n    const payload = action.payload;\n    const actionResult = actionQueue.action(prevState, payload);\n    function handleResult(nextState) {\n        // if we discarded this action, the state should also be discarded\n        if (action.discarded) {\n            return;\n        }\n        actionQueue.state = nextState;\n        runRemainingActions(actionQueue, setState);\n        action.resolve(nextState);\n    }\n    // if the action is a promise, set up a callback to resolve it\n    if ((0, _isthenable.isThenable)(actionResult)) {\n        actionResult.then(handleResult, (err)=>{\n            runRemainingActions(actionQueue, setState);\n            action.reject(err);\n        });\n    } else {\n        handleResult(actionResult);\n    }\n}\nfunction dispatchAction(actionQueue, payload, setState) {\n    let resolvers = {\n        resolve: setState,\n        reject: ()=>{}\n    };\n    // most of the action types are async with the exception of restore\n    // it's important that restore is handled quickly since it's fired on the popstate event\n    // and we don't want to add any delay on a back/forward nav\n    // this only creates a promise for the async actions\n    if (payload.type !== _routerreducertypes.ACTION_RESTORE) {\n        // Create the promise and assign the resolvers to the object.\n        const deferredPromise = new Promise((resolve, reject)=>{\n            resolvers = {\n                resolve,\n                reject\n            };\n        });\n        (0, _react.startTransition)(()=>{\n            // we immediately notify React of the pending promise -- the resolver is attached to the action node\n            // and will be called when the associated action promise resolves\n            setState(deferredPromise);\n        });\n    }\n    const newAction = {\n        payload,\n        next: null,\n        resolve: resolvers.resolve,\n        reject: resolvers.reject\n    };\n    // Check if the queue is empty\n    if (actionQueue.pending === null) {\n        // The queue is empty, so add the action and start it immediately\n        // Mark this action as the last in the queue\n        actionQueue.last = newAction;\n        runAction({\n            actionQueue,\n            action: newAction,\n            setState\n        });\n    } else if (payload.type === _routerreducertypes.ACTION_NAVIGATE || payload.type === _routerreducertypes.ACTION_RESTORE) {\n        // Navigations (including back/forward) take priority over any pending actions.\n        // Mark the pending action as discarded (so the state is never applied) and start the navigation action immediately.\n        actionQueue.pending.discarded = true;\n        // The rest of the current queue should still execute after this navigation.\n        // (Note that it can't contain any earlier navigations, because we always put those into `actionQueue.pending` by calling `runAction`)\n        newAction.next = actionQueue.pending.next;\n        // if the pending action was a server action, mark the queue as needing a refresh once events are processed\n        if (actionQueue.pending.payload.type === _routerreducertypes.ACTION_SERVER_ACTION) {\n            actionQueue.needsRefresh = true;\n        }\n        runAction({\n            actionQueue,\n            action: newAction,\n            setState\n        });\n    } else {\n        // The queue is not empty, so add the action to the end of the queue\n        // It will be started by runRemainingActions after the previous action finishes\n        if (actionQueue.last !== null) {\n            actionQueue.last.next = newAction;\n        }\n        actionQueue.last = newAction;\n    }\n}\nlet globalActionQueue = null;\nfunction createMutableActionQueue(initialState, instrumentationHooks) {\n    const actionQueue = {\n        state: initialState,\n        dispatch: (payload, setState)=>dispatchAction(actionQueue, payload, setState),\n        action: async (state, action)=>{\n            const result = (0, _routerreducer.reducer)(state, action);\n            return result;\n        },\n        pending: null,\n        last: null,\n        onRouterTransitionStart: instrumentationHooks !== null && typeof instrumentationHooks.onRouterTransitionStart === 'function' ? instrumentationHooks.onRouterTransitionStart : null\n    };\n    if (true) {\n        // The action queue is lazily created on hydration, but after that point\n        // it doesn't change. So we can store it in a global rather than pass\n        // it around everywhere via props/context.\n        if (globalActionQueue !== null) {\n            throw Object.defineProperty(new Error('Internal Next.js Error: createMutableActionQueue was called more ' + 'than once'), \"__NEXT_ERROR_CODE\", {\n                value: \"E624\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        globalActionQueue = actionQueue;\n    }\n    return actionQueue;\n}\nfunction getCurrentAppRouterState() {\n    return globalActionQueue !== null ? globalActionQueue.state : null;\n}\nfunction getAppRouterActionQueue() {\n    if (globalActionQueue === null) {\n        throw Object.defineProperty(new Error('Internal Next.js error: Router action dispatched before initialization.'), \"__NEXT_ERROR_CODE\", {\n            value: \"E668\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    return globalActionQueue;\n}\nfunction getProfilingHookForOnNavigationStart() {\n    if (globalActionQueue !== null) {\n        return globalActionQueue.onRouterTransitionStart;\n    }\n    return null;\n}\nfunction dispatchNavigateAction(href, navigateType, shouldScroll, linkInstanceRef) {\n    // TODO: This stuff could just go into the reducer. Leaving as-is for now\n    // since we're about to rewrite all the router reducer stuff anyway.\n    const url = new URL((0, _addbasepath.addBasePath)(href), location.href);\n    if (false) {}\n    (0, _links.setLinkForCurrentNavigation)(linkInstanceRef);\n    const onRouterTransitionStart = getProfilingHookForOnNavigationStart();\n    if (onRouterTransitionStart !== null) {\n        onRouterTransitionStart(href, navigateType);\n    }\n    (0, _useactionqueue.dispatchAppRouterAction)({\n        type: _routerreducertypes.ACTION_NAVIGATE,\n        url,\n        isExternalUrl: (0, _approuter.isExternalURL)(url),\n        locationSearch: location.search,\n        shouldScroll,\n        navigateType,\n        allowAliasing: true\n    });\n}\nfunction dispatchTraverseAction(href, tree) {\n    const onRouterTransitionStart = getProfilingHookForOnNavigationStart();\n    if (onRouterTransitionStart !== null) {\n        onRouterTransitionStart(href, 'traverse');\n    }\n    (0, _useactionqueue.dispatchAppRouterAction)({\n        type: _routerreducertypes.ACTION_RESTORE,\n        url: new URL(href),\n        tree\n    });\n}\nconst publicAppRouterInstance = {\n    back: ()=>window.history.back(),\n    forward: ()=>window.history.forward(),\n    prefetch:  false ? // cache. So we don't need to dispatch an action.\n    0 : (href, options)=>{\n        // Use the old prefetch implementation.\n        const actionQueue = getAppRouterActionQueue();\n        const url = (0, _approuter.createPrefetchURL)(href);\n        if (url !== null) {\n            var _options_kind;\n            // The prefetch reducer doesn't actually update any state or\n            // trigger a rerender. It just writes to a mutable cache. So we\n            // shouldn't bother calling setState/dispatch; we can just re-run\n            // the reducer directly using the current state.\n            // TODO: Refactor this away from a \"reducer\" so it's\n            // less confusing.\n            (0, _prefetchreducer.prefetchReducer)(actionQueue.state, {\n                type: _routerreducertypes.ACTION_PREFETCH,\n                url,\n                kind: (_options_kind = options == null ? void 0 : options.kind) != null ? _options_kind : _routerreducertypes.PrefetchKind.FULL\n            });\n        }\n    },\n    replace: (href, options)=>{\n        (0, _react.startTransition)(()=>{\n            var _options_scroll;\n            dispatchNavigateAction(href, 'replace', (_options_scroll = options == null ? void 0 : options.scroll) != null ? _options_scroll : true, null);\n        });\n    },\n    push: (href, options)=>{\n        (0, _react.startTransition)(()=>{\n            var _options_scroll;\n            dispatchNavigateAction(href, 'push', (_options_scroll = options == null ? void 0 : options.scroll) != null ? _options_scroll : true, null);\n        });\n    },\n    refresh: ()=>{\n        (0, _react.startTransition)(()=>{\n            (0, _useactionqueue.dispatchAppRouterAction)({\n                type: _routerreducertypes.ACTION_REFRESH,\n                origin: window.location.origin\n            });\n        });\n    },\n    hmrRefresh: ()=>{\n        if (false) {} else {\n            (0, _react.startTransition)(()=>{\n                (0, _useactionqueue.dispatchAppRouterAction)({\n                    type: _routerreducertypes.ACTION_HMR_REFRESH,\n                    origin: window.location.origin\n                });\n            });\n        }\n    }\n};\n// Exists for debugging purposes. Don't use in application code.\nif ( true && window.next) {\n    window.next.router = publicAppRouterInstance;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-router-instance.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvYXBwLXJvdXRlci1pbnN0YW5jZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7SUF5TWdCQSx3QkFBd0I7ZUFBeEJBOztJQTBEQUMsc0JBQXNCO2VBQXRCQTs7SUErQkFDLHNCQUFzQjtlQUF0QkE7O0lBbkRBQyx3QkFBd0I7ZUFBeEJBOztJQXVFSEMsdUJBQXVCO2VBQXZCQTs7O2dEQTFTTjsyQ0FDaUI7bUNBQ1E7d0NBQ0w7MENBQzBCOzRDQUNiO3lDQUNaO3VDQUNxQjs2Q0FDakI7bUNBTStCO0FBNEIvRCxTQUFTQyxvQkFDUEMsV0FBaUMsRUFDakNDLFFBQThCO0lBRTlCLElBQUlELFlBQVlFLE9BQU8sS0FBSyxNQUFNO1FBQ2hDRixZQUFZRSxPQUFPLEdBQUdGLFlBQVlFLE9BQU8sQ0FBQ0MsSUFBSTtRQUM5QyxJQUFJSCxZQUFZRSxPQUFPLEtBQUssTUFBTTtZQUNoQyxtRUFBbUU7WUFDbkVFLFVBQVU7Z0JBQ1JKO2dCQUNBSyxRQUFRTCxZQUFZRSxPQUFPO2dCQUMzQkQ7WUFDRjtRQUNGLE9BQU87WUFDTCw0REFBNEQ7WUFDNUQsSUFBSUQsWUFBWU0sWUFBWSxFQUFFO2dCQUM1Qk4sWUFBWU0sWUFBWSxHQUFHO2dCQUMzQk4sWUFBWU8sUUFBUSxDQUNsQjtvQkFDRUMsTUFBTUMsb0JBQUFBLGNBQWM7b0JBQ3BCQyxRQUFRQyxPQUFPQyxRQUFRLENBQUNGLE1BQU07Z0JBQ2hDLEdBQ0FUO1lBRUo7UUFDRjtJQUNGO0FBQ0Y7QUFFQSxlQUFlRyxVQUFVLEtBUXhCO0lBUndCLE1BQ3ZCSixXQUFXLEVBQ1hLLE1BQU0sRUFDTkosUUFBUSxFQUtULEdBUndCO0lBU3ZCLE1BQU1ZLFlBQVliLFlBQVljLEtBQUs7SUFFbkNkLFlBQVlFLE9BQU8sR0FBR0c7SUFFdEIsTUFBTVUsVUFBVVYsT0FBT1UsT0FBTztJQUM5QixNQUFNQyxlQUFlaEIsWUFBWUssTUFBTSxDQUFDUSxXQUFXRTtJQUVuRCxTQUFTRSxhQUFhQyxTQUF5QjtRQUM3QyxrRUFBa0U7UUFDbEUsSUFBSWIsT0FBT2MsU0FBUyxFQUFFO1lBQ3BCO1FBQ0Y7UUFFQW5CLFlBQVljLEtBQUssR0FBR0k7UUFFcEJuQixvQkFBb0JDLGFBQWFDO1FBQ2pDSSxPQUFPZSxPQUFPLENBQUNGO0lBQ2pCO0lBRUEsOERBQThEO0lBQzlELElBQUlHLENBQUFBLEdBQUFBLFlBQUFBLFVBQVUsRUFBQ0wsZUFBZTtRQUM1QkEsYUFBYU0sSUFBSSxDQUFDTCxjQUFjLENBQUNNO1lBQy9CeEIsb0JBQW9CQyxhQUFhQztZQUNqQ0ksT0FBT21CLE1BQU0sQ0FBQ0Q7UUFDaEI7SUFDRixPQUFPO1FBQ0xOLGFBQWFEO0lBQ2Y7QUFDRjtBQUVBLFNBQVNTLGVBQ1B6QixXQUFpQyxFQUNqQ2UsT0FBdUIsRUFDdkJkLFFBQThCO0lBRTlCLElBQUl5QixZQUdBO1FBQUVOLFNBQVNuQjtRQUFVdUIsUUFBUSxLQUFPO0lBQUU7SUFFMUMsbUVBQW1FO0lBQ25FLHdGQUF3RjtJQUN4RiwyREFBMkQ7SUFDM0Qsb0RBQW9EO0lBQ3BELElBQUlULFFBQVFQLElBQUksS0FBS21CLG9CQUFBQSxjQUFjLEVBQUU7UUFDbkMsNkRBQTZEO1FBQzdELE1BQU1DLGtCQUFrQixJQUFJQyxRQUF3QixDQUFDVCxTQUFTSTtZQUM1REUsWUFBWTtnQkFBRU47Z0JBQVNJO1lBQU87UUFDaEM7UUFFQU0sQ0FBQUEsR0FBQUEsT0FBQUEsZUFBZSxFQUFDO1lBQ2Qsb0dBQW9HO1lBQ3BHLGlFQUFpRTtZQUNqRTdCLFNBQVMyQjtRQUNYO0lBQ0Y7SUFFQSxNQUFNRyxZQUE2QjtRQUNqQ2hCO1FBQ0FaLE1BQU07UUFDTmlCLFNBQVNNLFVBQVVOLE9BQU87UUFDMUJJLFFBQVFFLFVBQVVGLE1BQU07SUFDMUI7SUFFQSw4QkFBOEI7SUFDOUIsSUFBSXhCLFlBQVlFLE9BQU8sS0FBSyxNQUFNO1FBQ2hDLGlFQUFpRTtRQUNqRSw0Q0FBNEM7UUFDNUNGLFlBQVlnQyxJQUFJLEdBQUdEO1FBRW5CM0IsVUFBVTtZQUNSSjtZQUNBSyxRQUFRMEI7WUFDUjlCO1FBQ0Y7SUFDRixPQUFPLElBQ0xjLFFBQVFQLElBQUksS0FBS3lCLG9CQUFBQSxlQUFlLElBQ2hDbEIsUUFBUVAsSUFBSSxLQUFLbUIsb0JBQUFBLGNBQWMsRUFDL0I7UUFDQSwrRUFBK0U7UUFDL0Usb0hBQW9IO1FBQ3BIM0IsWUFBWUUsT0FBTyxDQUFDaUIsU0FBUyxHQUFHO1FBRWhDLDRFQUE0RTtRQUM1RSxzSUFBc0k7UUFDdElZLFVBQVU1QixJQUFJLEdBQUdILFlBQVlFLE9BQU8sQ0FBQ0MsSUFBSTtRQUV6QywyR0FBMkc7UUFDM0csSUFBSUgsWUFBWUUsT0FBTyxDQUFDYSxPQUFPLENBQUNQLElBQUksS0FBSzBCLG9CQUFBQSxvQkFBb0IsRUFBRTtZQUM3RGxDLFlBQVlNLFlBQVksR0FBRztRQUM3QjtRQUVBRixVQUFVO1lBQ1JKO1lBQ0FLLFFBQVEwQjtZQUNSOUI7UUFDRjtJQUNGLE9BQU87UUFDTCxvRUFBb0U7UUFDcEUsK0VBQStFO1FBQy9FLElBQUlELFlBQVlnQyxJQUFJLEtBQUssTUFBTTtZQUM3QmhDLFlBQVlnQyxJQUFJLENBQUM3QixJQUFJLEdBQUc0QjtRQUMxQjtRQUNBL0IsWUFBWWdDLElBQUksR0FBR0Q7SUFDckI7QUFDRjtBQUVBLElBQUlJLG9CQUFpRDtBQUU5QyxTQUFTekMseUJBQ2QwQyxZQUE0QixFQUM1QkMsb0JBQXVEO0lBRXZELE1BQU1yQyxjQUFvQztRQUN4Q2MsT0FBT3NCO1FBQ1A3QixVQUFVLENBQUNRLFNBQXlCZCxXQUNsQ3dCLGVBQWV6QixhQUFhZSxTQUFTZDtRQUN2Q0ksUUFBUSxPQUFPUyxPQUF1QlQ7WUFDcEMsTUFBTWlDLFNBQVNDLENBQUFBLEdBQUFBLGVBQUFBLE9BQUFBLEVBQVF6QixPQUFPVDtZQUM5QixPQUFPaUM7UUFDVDtRQUNBcEMsU0FBUztRQUNUOEIsTUFBTTtRQUNOUSx5QkFDRUgseUJBQXlCLFFBQ3pCLE9BQU9BLHFCQUFxQkcsdUJBQXVCLEtBQUssYUFFcERILHFCQUFxQkcsdUJBQXVCLEdBQzVDO0lBQ1I7SUFFQSxJQUFJLElBQTZCLEVBQUU7UUFDakMsd0VBQXdFO1FBQ3hFLHFFQUFxRTtRQUNyRSwwQ0FBMEM7UUFDMUMsSUFBSUwsc0JBQXNCLE1BQU07WUFDOUIsTUFBTSxxQkFHTCxDQUhLLElBQUlNLE1BQ1Isc0VBQ0UsY0FGRTt1QkFBQTs0QkFBQTs4QkFBQTtZQUdOO1FBQ0Y7UUFDQU4sb0JBQW9CbkM7SUFDdEI7SUFFQSxPQUFPQTtBQUNUO0FBRU8sU0FBU0g7SUFDZCxPQUFPc0Msc0JBQXNCLE9BQU9BLGtCQUFrQnJCLEtBQUssR0FBRztBQUNoRTtBQUVBLFNBQVM0QjtJQUNQLElBQUlQLHNCQUFzQixNQUFNO1FBQzlCLE1BQU0scUJBRUwsQ0FGSyxJQUFJTSxNQUNSLDRFQURJO21CQUFBO3dCQUFBOzBCQUFBO1FBRU47SUFDRjtJQUNBLE9BQU9OO0FBQ1Q7QUFFQSxTQUFTUTtJQUNQLElBQUlSLHNCQUFzQixNQUFNO1FBQzlCLE9BQU9BLGtCQUFrQkssdUJBQXVCO0lBQ2xEO0lBQ0EsT0FBTztBQUNUO0FBRU8sU0FBUzdDLHVCQUNkaUQsSUFBWSxFQUNaQyxZQUE0QyxFQUM1Q0MsWUFBcUIsRUFDckJDLGVBQW9DO0lBRXBDLHlFQUF5RTtJQUN6RSxvRUFBb0U7SUFDcEUsTUFBTUMsTUFBTSxJQUFJQyxJQUFJQyxDQUFBQSxHQUFBQSxhQUFBQSxXQUFBQSxFQUFZTixPQUFPaEMsU0FBU2dDLElBQUk7SUFDcEQsSUFBSU8sS0FBd0MsRUFBRSxFQUU3QztJQUVESSxDQUFBQSxHQUFBQSxPQUFBQSwyQkFBQUEsRUFBNEJSO0lBRTVCLE1BQU1QLDBCQUEwQkc7SUFDaEMsSUFBSUgsNEJBQTRCLE1BQU07UUFDcENBLHdCQUF3QkksTUFBTUM7SUFDaEM7SUFFQVcsQ0FBQUEsR0FBQUEsZ0JBQUFBLHVCQUFBQSxFQUF3QjtRQUN0QmhELE1BQU15QixvQkFBQUEsZUFBZTtRQUNyQmU7UUFDQVMsZUFBZUMsQ0FBQUEsR0FBQUEsV0FBQUEsYUFBQUEsRUFBY1Y7UUFDN0JXLGdCQUFnQi9DLFNBQVNnRCxNQUFNO1FBQy9CZDtRQUNBRDtRQUNBZ0IsZUFBZTtJQUNqQjtBQUNGO0FBRU8sU0FBU2pFLHVCQUNkZ0QsSUFBWSxFQUNaa0IsSUFBbUM7SUFFbkMsTUFBTXRCLDBCQUEwQkc7SUFDaEMsSUFBSUgsNEJBQTRCLE1BQU07UUFDcENBLHdCQUF3QkksTUFBTTtJQUNoQztJQUNBWSxDQUFBQSxHQUFBQSxnQkFBQUEsdUJBQUFBLEVBQXdCO1FBQ3RCaEQsTUFBTW1CLG9CQUFBQSxjQUFjO1FBQ3BCcUIsS0FBSyxJQUFJQyxJQUFJTDtRQUNia0I7SUFDRjtBQUNGO0FBT08sTUFBTWhFLDBCQUE2QztJQUN4RGlFLE1BQU0sSUFBTXBELE9BQU9xRCxPQUFPLENBQUNELElBQUk7SUFDL0JFLFNBQVMsSUFBTXRELE9BQU9xRCxPQUFPLENBQUNDLE9BQU87SUFDckNDLFVBQVVmLE1BQXVDLEdBRTdDLGlEQUNpRDtJQUNqRCxDQVFDLEdBQ0QsQ0FBQ1AsTUFBY3dCO1FBQ2IsdUNBQXVDO1FBQ3ZDLE1BQU1wRSxjQUFjMEM7UUFDcEIsTUFBTU0sTUFBTTBCLENBQUFBLEdBQUFBLFdBQUFBLGlCQUFBQSxFQUFrQjlCO1FBQzlCLElBQUlJLFFBQVEsTUFBTTtnQkFVUm9CO1lBVFIsNERBQTREO1lBQzVELCtEQUErRDtZQUMvRCxpRUFBaUU7WUFDakUsZ0RBQWdEO1lBQ2hELG9EQUFvRDtZQUNwRCxrQkFBa0I7WUFDbEJPLENBQUFBLEdBQUFBLGlCQUFBQSxlQUFBQSxFQUFnQjNFLFlBQVljLEtBQUssRUFBRTtnQkFDakNOLE1BQU1vRSxvQkFBQUEsZUFBZTtnQkFDckI1QjtnQkFDQXVCLE1BQU1ILENBQUFBLGdCQUFBQSxXQUFBQSxPQUFBQSxLQUFBQSxJQUFBQSxRQUFTRyxJQUFBQSxLQUFJLE9BQWJILGdCQUFpQkksb0JBQUFBLFlBQVksQ0FBQ0MsSUFBSTtZQUMxQztRQUNGO0lBQ0Y7SUFDSkksU0FBUyxDQUFDakMsTUFBY3dCO1FBQ3RCdEMsQ0FBQUEsR0FBQUEsT0FBQUEsZUFBQUEsRUFBZ0I7Z0JBQzBCc0M7WUFBeEN6RSx1QkFBdUJpRCxNQUFNLFdBQVd3QixDQUFBQSxrQkFBQUEsV0FBQUEsT0FBQUEsS0FBQUEsSUFBQUEsUUFBU1UsTUFBQUEsS0FBTSxPQUFmVixrQkFBbUIsTUFBTTtRQUNuRTtJQUNGO0lBQ0FXLE1BQU0sQ0FBQ25DLE1BQWN3QjtRQUNuQnRDLENBQUFBLEdBQUFBLE9BQUFBLGVBQUFBLEVBQWdCO2dCQUN1QnNDO1lBQXJDekUsdUJBQXVCaUQsTUFBTSxRQUFRd0IsQ0FBQUEsa0JBQUFBLFdBQUFBLE9BQUFBLEtBQUFBLElBQUFBLFFBQVNVLE1BQUFBLEtBQU0sT0FBZlYsa0JBQW1CLE1BQU07UUFDaEU7SUFDRjtJQUNBWSxTQUFTO1FBQ1BsRCxDQUFBQSxHQUFBQSxPQUFBQSxlQUFBQSxFQUFnQjtZQUNkMEIsQ0FBQUEsR0FBQUEsZ0JBQUFBLHVCQUFBQSxFQUF3QjtnQkFDdEJoRCxNQUFNQyxvQkFBQUEsY0FBYztnQkFDcEJDLFFBQVFDLE9BQU9DLFFBQVEsQ0FBQ0YsTUFBTTtZQUNoQztRQUNGO0lBQ0Y7SUFDQXVFLFlBQVk7UUFDVixJQUFJOUIsS0FBb0IsRUFBb0IsRUFJM0MsTUFBTTtZQUNMckIsQ0FBQUEsR0FBQUEsT0FBQUEsZUFBZSxFQUFDO2dCQUNkMEIsQ0FBQUEsR0FBQUEsZ0JBQUFBLHVCQUFBQSxFQUF3QjtvQkFDdEJoRCxNQUFNMkUsb0JBQUFBLGtCQUFrQjtvQkFDeEJ6RSxRQUFRQyxPQUFPQyxRQUFRLENBQUNGLE1BQU07Z0JBQ2hDO1lBQ0Y7UUFDRjtJQUNGO0FBQ0Y7QUFFQSxnRUFBZ0U7QUFDaEUsSUFBSSxLQUE2QixJQUFJQyxPQUFPUixJQUFJLEVBQUU7SUFDaERRLE9BQU9SLElBQUksQ0FBQ2lGLE1BQU0sR0FBR3RGO0FBQ3ZCIiwic291cmNlcyI6WyJDOlxcc3JjXFxjbGllbnRcXGNvbXBvbmVudHNcXGFwcC1yb3V0ZXItaW5zdGFuY2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtcbiAgdHlwZSBBcHBSb3V0ZXJTdGF0ZSxcbiAgdHlwZSBSZWR1Y2VyQWN0aW9ucyxcbiAgdHlwZSBSZWR1Y2VyU3RhdGUsXG4gIEFDVElPTl9SRUZSRVNILFxuICBBQ1RJT05fU0VSVkVSX0FDVElPTixcbiAgQUNUSU9OX05BVklHQVRFLFxuICBBQ1RJT05fUkVTVE9SRSxcbiAgdHlwZSBOYXZpZ2F0ZUFjdGlvbixcbiAgQUNUSU9OX0hNUl9SRUZSRVNILFxuICBQcmVmZXRjaEtpbmQsXG4gIEFDVElPTl9QUkVGRVRDSCxcbn0gZnJvbSAnLi9yb3V0ZXItcmVkdWNlci9yb3V0ZXItcmVkdWNlci10eXBlcydcbmltcG9ydCB7IHJlZHVjZXIgfSBmcm9tICcuL3JvdXRlci1yZWR1Y2VyL3JvdXRlci1yZWR1Y2VyJ1xuaW1wb3J0IHsgc3RhcnRUcmFuc2l0aW9uIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBpc1RoZW5hYmxlIH0gZnJvbSAnLi4vLi4vc2hhcmVkL2xpYi9pcy10aGVuYWJsZSdcbmltcG9ydCB7IHByZWZldGNoIGFzIHByZWZldGNoV2l0aFNlZ21lbnRDYWNoZSB9IGZyb20gJy4vc2VnbWVudC1jYWNoZSdcbmltcG9ydCB7IGRpc3BhdGNoQXBwUm91dGVyQWN0aW9uIH0gZnJvbSAnLi91c2UtYWN0aW9uLXF1ZXVlJ1xuaW1wb3J0IHsgYWRkQmFzZVBhdGggfSBmcm9tICcuLi9hZGQtYmFzZS1wYXRoJ1xuaW1wb3J0IHsgY3JlYXRlUHJlZmV0Y2hVUkwsIGlzRXh0ZXJuYWxVUkwgfSBmcm9tICcuL2FwcC1yb3V0ZXInXG5pbXBvcnQgeyBwcmVmZXRjaFJlZHVjZXIgfSBmcm9tICcuL3JvdXRlci1yZWR1Y2VyL3JlZHVjZXJzL3ByZWZldGNoLXJlZHVjZXInXG5pbXBvcnQgdHlwZSB7XG4gIEFwcFJvdXRlckluc3RhbmNlLFxuICBOYXZpZ2F0ZU9wdGlvbnMsXG4gIFByZWZldGNoT3B0aW9ucyxcbn0gZnJvbSAnLi4vLi4vc2hhcmVkL2xpYi9hcHAtcm91dGVyLWNvbnRleHQuc2hhcmVkLXJ1bnRpbWUnXG5pbXBvcnQgeyBzZXRMaW5rRm9yQ3VycmVudE5hdmlnYXRpb24sIHR5cGUgTGlua0luc3RhbmNlIH0gZnJvbSAnLi9saW5rcydcbmltcG9ydCB0eXBlIHsgRmxpZ2h0Um91dGVyU3RhdGUgfSBmcm9tICcuLi8uLi9zZXJ2ZXIvYXBwLXJlbmRlci90eXBlcydcbmltcG9ydCB0eXBlIHsgQ2xpZW50SW5zdHJ1bWVudGF0aW9uSG9va3MgfSBmcm9tICcuLi9hcHAtaW5kZXgnXG5cbmV4cG9ydCB0eXBlIERpc3BhdGNoU3RhdGVQcm9taXNlID0gUmVhY3QuRGlzcGF0Y2g8UmVkdWNlclN0YXRlPlxuXG5leHBvcnQgdHlwZSBBcHBSb3V0ZXJBY3Rpb25RdWV1ZSA9IHtcbiAgc3RhdGU6IEFwcFJvdXRlclN0YXRlXG4gIGRpc3BhdGNoOiAocGF5bG9hZDogUmVkdWNlckFjdGlvbnMsIHNldFN0YXRlOiBEaXNwYXRjaFN0YXRlUHJvbWlzZSkgPT4gdm9pZFxuICBhY3Rpb246IChzdGF0ZTogQXBwUm91dGVyU3RhdGUsIGFjdGlvbjogUmVkdWNlckFjdGlvbnMpID0+IFJlZHVjZXJTdGF0ZVxuXG4gIG9uUm91dGVyVHJhbnNpdGlvblN0YXJ0OlxuICAgIHwgKCh1cmw6IHN0cmluZywgdHlwZTogJ3B1c2gnIHwgJ3JlcGxhY2UnIHwgJ3RyYXZlcnNlJykgPT4gdm9pZClcbiAgICB8IG51bGxcblxuICBwZW5kaW5nOiBBY3Rpb25RdWV1ZU5vZGUgfCBudWxsXG4gIG5lZWRzUmVmcmVzaD86IGJvb2xlYW5cbiAgbGFzdDogQWN0aW9uUXVldWVOb2RlIHwgbnVsbFxufVxuXG5leHBvcnQgdHlwZSBBY3Rpb25RdWV1ZU5vZGUgPSB7XG4gIHBheWxvYWQ6IFJlZHVjZXJBY3Rpb25zXG4gIG5leHQ6IEFjdGlvblF1ZXVlTm9kZSB8IG51bGxcbiAgcmVzb2x2ZTogKHZhbHVlOiBSZWR1Y2VyU3RhdGUpID0+IHZvaWRcbiAgcmVqZWN0OiAoZXJyOiBFcnJvcikgPT4gdm9pZFxuICBkaXNjYXJkZWQ/OiBib29sZWFuXG59XG5cbmZ1bmN0aW9uIHJ1blJlbWFpbmluZ0FjdGlvbnMoXG4gIGFjdGlvblF1ZXVlOiBBcHBSb3V0ZXJBY3Rpb25RdWV1ZSxcbiAgc2V0U3RhdGU6IERpc3BhdGNoU3RhdGVQcm9taXNlXG4pIHtcbiAgaWYgKGFjdGlvblF1ZXVlLnBlbmRpbmcgIT09IG51bGwpIHtcbiAgICBhY3Rpb25RdWV1ZS5wZW5kaW5nID0gYWN0aW9uUXVldWUucGVuZGluZy5uZXh0XG4gICAgaWYgKGFjdGlvblF1ZXVlLnBlbmRpbmcgIT09IG51bGwpIHtcbiAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvbm8tdXNlLWJlZm9yZS1kZWZpbmVcbiAgICAgIHJ1bkFjdGlvbih7XG4gICAgICAgIGFjdGlvblF1ZXVlLFxuICAgICAgICBhY3Rpb246IGFjdGlvblF1ZXVlLnBlbmRpbmcsXG4gICAgICAgIHNldFN0YXRlLFxuICAgICAgfSlcbiAgICB9IGVsc2Uge1xuICAgICAgLy8gTm8gbW9yZSBhY3Rpb25zIGFyZSBwZW5kaW5nLCBjaGVjayBpZiBhIHJlZnJlc2ggaXMgbmVlZGVkXG4gICAgICBpZiAoYWN0aW9uUXVldWUubmVlZHNSZWZyZXNoKSB7XG4gICAgICAgIGFjdGlvblF1ZXVlLm5lZWRzUmVmcmVzaCA9IGZhbHNlXG4gICAgICAgIGFjdGlvblF1ZXVlLmRpc3BhdGNoKFxuICAgICAgICAgIHtcbiAgICAgICAgICAgIHR5cGU6IEFDVElPTl9SRUZSRVNILFxuICAgICAgICAgICAgb3JpZ2luOiB3aW5kb3cubG9jYXRpb24ub3JpZ2luLFxuICAgICAgICAgIH0sXG4gICAgICAgICAgc2V0U3RhdGVcbiAgICAgICAgKVxuICAgICAgfVxuICAgIH1cbiAgfVxufVxuXG5hc3luYyBmdW5jdGlvbiBydW5BY3Rpb24oe1xuICBhY3Rpb25RdWV1ZSxcbiAgYWN0aW9uLFxuICBzZXRTdGF0ZSxcbn06IHtcbiAgYWN0aW9uUXVldWU6IEFwcFJvdXRlckFjdGlvblF1ZXVlXG4gIGFjdGlvbjogQWN0aW9uUXVldWVOb2RlXG4gIHNldFN0YXRlOiBEaXNwYXRjaFN0YXRlUHJvbWlzZVxufSkge1xuICBjb25zdCBwcmV2U3RhdGUgPSBhY3Rpb25RdWV1ZS5zdGF0ZVxuXG4gIGFjdGlvblF1ZXVlLnBlbmRpbmcgPSBhY3Rpb25cblxuICBjb25zdCBwYXlsb2FkID0gYWN0aW9uLnBheWxvYWRcbiAgY29uc3QgYWN0aW9uUmVzdWx0ID0gYWN0aW9uUXVldWUuYWN0aW9uKHByZXZTdGF0ZSwgcGF5bG9hZClcblxuICBmdW5jdGlvbiBoYW5kbGVSZXN1bHQobmV4dFN0YXRlOiBBcHBSb3V0ZXJTdGF0ZSkge1xuICAgIC8vIGlmIHdlIGRpc2NhcmRlZCB0aGlzIGFjdGlvbiwgdGhlIHN0YXRlIHNob3VsZCBhbHNvIGJlIGRpc2NhcmRlZFxuICAgIGlmIChhY3Rpb24uZGlzY2FyZGVkKSB7XG4gICAgICByZXR1cm5cbiAgICB9XG5cbiAgICBhY3Rpb25RdWV1ZS5zdGF0ZSA9IG5leHRTdGF0ZVxuXG4gICAgcnVuUmVtYWluaW5nQWN0aW9ucyhhY3Rpb25RdWV1ZSwgc2V0U3RhdGUpXG4gICAgYWN0aW9uLnJlc29sdmUobmV4dFN0YXRlKVxuICB9XG5cbiAgLy8gaWYgdGhlIGFjdGlvbiBpcyBhIHByb21pc2UsIHNldCB1cCBhIGNhbGxiYWNrIHRvIHJlc29sdmUgaXRcbiAgaWYgKGlzVGhlbmFibGUoYWN0aW9uUmVzdWx0KSkge1xuICAgIGFjdGlvblJlc3VsdC50aGVuKGhhbmRsZVJlc3VsdCwgKGVycikgPT4ge1xuICAgICAgcnVuUmVtYWluaW5nQWN0aW9ucyhhY3Rpb25RdWV1ZSwgc2V0U3RhdGUpXG4gICAgICBhY3Rpb24ucmVqZWN0KGVycilcbiAgICB9KVxuICB9IGVsc2Uge1xuICAgIGhhbmRsZVJlc3VsdChhY3Rpb25SZXN1bHQpXG4gIH1cbn1cblxuZnVuY3Rpb24gZGlzcGF0Y2hBY3Rpb24oXG4gIGFjdGlvblF1ZXVlOiBBcHBSb3V0ZXJBY3Rpb25RdWV1ZSxcbiAgcGF5bG9hZDogUmVkdWNlckFjdGlvbnMsXG4gIHNldFN0YXRlOiBEaXNwYXRjaFN0YXRlUHJvbWlzZVxuKSB7XG4gIGxldCByZXNvbHZlcnM6IHtcbiAgICByZXNvbHZlOiAodmFsdWU6IFJlZHVjZXJTdGF0ZSkgPT4gdm9pZFxuICAgIHJlamVjdDogKHJlYXNvbjogYW55KSA9PiB2b2lkXG4gIH0gPSB7IHJlc29sdmU6IHNldFN0YXRlLCByZWplY3Q6ICgpID0+IHt9IH1cblxuICAvLyBtb3N0IG9mIHRoZSBhY3Rpb24gdHlwZXMgYXJlIGFzeW5jIHdpdGggdGhlIGV4Y2VwdGlvbiBvZiByZXN0b3JlXG4gIC8vIGl0J3MgaW1wb3J0YW50IHRoYXQgcmVzdG9yZSBpcyBoYW5kbGVkIHF1aWNrbHkgc2luY2UgaXQncyBmaXJlZCBvbiB0aGUgcG9wc3RhdGUgZXZlbnRcbiAgLy8gYW5kIHdlIGRvbid0IHdhbnQgdG8gYWRkIGFueSBkZWxheSBvbiBhIGJhY2svZm9yd2FyZCBuYXZcbiAgLy8gdGhpcyBvbmx5IGNyZWF0ZXMgYSBwcm9taXNlIGZvciB0aGUgYXN5bmMgYWN0aW9uc1xuICBpZiAocGF5bG9hZC50eXBlICE9PSBBQ1RJT05fUkVTVE9SRSkge1xuICAgIC8vIENyZWF0ZSB0aGUgcHJvbWlzZSBhbmQgYXNzaWduIHRoZSByZXNvbHZlcnMgdG8gdGhlIG9iamVjdC5cbiAgICBjb25zdCBkZWZlcnJlZFByb21pc2UgPSBuZXcgUHJvbWlzZTxBcHBSb3V0ZXJTdGF0ZT4oKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgcmVzb2x2ZXJzID0geyByZXNvbHZlLCByZWplY3QgfVxuICAgIH0pXG5cbiAgICBzdGFydFRyYW5zaXRpb24oKCkgPT4ge1xuICAgICAgLy8gd2UgaW1tZWRpYXRlbHkgbm90aWZ5IFJlYWN0IG9mIHRoZSBwZW5kaW5nIHByb21pc2UgLS0gdGhlIHJlc29sdmVyIGlzIGF0dGFjaGVkIHRvIHRoZSBhY3Rpb24gbm9kZVxuICAgICAgLy8gYW5kIHdpbGwgYmUgY2FsbGVkIHdoZW4gdGhlIGFzc29jaWF0ZWQgYWN0aW9uIHByb21pc2UgcmVzb2x2ZXNcbiAgICAgIHNldFN0YXRlKGRlZmVycmVkUHJvbWlzZSlcbiAgICB9KVxuICB9XG5cbiAgY29uc3QgbmV3QWN0aW9uOiBBY3Rpb25RdWV1ZU5vZGUgPSB7XG4gICAgcGF5bG9hZCxcbiAgICBuZXh0OiBudWxsLFxuICAgIHJlc29sdmU6IHJlc29sdmVycy5yZXNvbHZlLFxuICAgIHJlamVjdDogcmVzb2x2ZXJzLnJlamVjdCxcbiAgfVxuXG4gIC8vIENoZWNrIGlmIHRoZSBxdWV1ZSBpcyBlbXB0eVxuICBpZiAoYWN0aW9uUXVldWUucGVuZGluZyA9PT0gbnVsbCkge1xuICAgIC8vIFRoZSBxdWV1ZSBpcyBlbXB0eSwgc28gYWRkIHRoZSBhY3Rpb24gYW5kIHN0YXJ0IGl0IGltbWVkaWF0ZWx5XG4gICAgLy8gTWFyayB0aGlzIGFjdGlvbiBhcyB0aGUgbGFzdCBpbiB0aGUgcXVldWVcbiAgICBhY3Rpb25RdWV1ZS5sYXN0ID0gbmV3QWN0aW9uXG5cbiAgICBydW5BY3Rpb24oe1xuICAgICAgYWN0aW9uUXVldWUsXG4gICAgICBhY3Rpb246IG5ld0FjdGlvbixcbiAgICAgIHNldFN0YXRlLFxuICAgIH0pXG4gIH0gZWxzZSBpZiAoXG4gICAgcGF5bG9hZC50eXBlID09PSBBQ1RJT05fTkFWSUdBVEUgfHxcbiAgICBwYXlsb2FkLnR5cGUgPT09IEFDVElPTl9SRVNUT1JFXG4gICkge1xuICAgIC8vIE5hdmlnYXRpb25zIChpbmNsdWRpbmcgYmFjay9mb3J3YXJkKSB0YWtlIHByaW9yaXR5IG92ZXIgYW55IHBlbmRpbmcgYWN0aW9ucy5cbiAgICAvLyBNYXJrIHRoZSBwZW5kaW5nIGFjdGlvbiBhcyBkaXNjYXJkZWQgKHNvIHRoZSBzdGF0ZSBpcyBuZXZlciBhcHBsaWVkKSBhbmQgc3RhcnQgdGhlIG5hdmlnYXRpb24gYWN0aW9uIGltbWVkaWF0ZWx5LlxuICAgIGFjdGlvblF1ZXVlLnBlbmRpbmcuZGlzY2FyZGVkID0gdHJ1ZVxuXG4gICAgLy8gVGhlIHJlc3Qgb2YgdGhlIGN1cnJlbnQgcXVldWUgc2hvdWxkIHN0aWxsIGV4ZWN1dGUgYWZ0ZXIgdGhpcyBuYXZpZ2F0aW9uLlxuICAgIC8vIChOb3RlIHRoYXQgaXQgY2FuJ3QgY29udGFpbiBhbnkgZWFybGllciBuYXZpZ2F0aW9ucywgYmVjYXVzZSB3ZSBhbHdheXMgcHV0IHRob3NlIGludG8gYGFjdGlvblF1ZXVlLnBlbmRpbmdgIGJ5IGNhbGxpbmcgYHJ1bkFjdGlvbmApXG4gICAgbmV3QWN0aW9uLm5leHQgPSBhY3Rpb25RdWV1ZS5wZW5kaW5nLm5leHRcblxuICAgIC8vIGlmIHRoZSBwZW5kaW5nIGFjdGlvbiB3YXMgYSBzZXJ2ZXIgYWN0aW9uLCBtYXJrIHRoZSBxdWV1ZSBhcyBuZWVkaW5nIGEgcmVmcmVzaCBvbmNlIGV2ZW50cyBhcmUgcHJvY2Vzc2VkXG4gICAgaWYgKGFjdGlvblF1ZXVlLnBlbmRpbmcucGF5bG9hZC50eXBlID09PSBBQ1RJT05fU0VSVkVSX0FDVElPTikge1xuICAgICAgYWN0aW9uUXVldWUubmVlZHNSZWZyZXNoID0gdHJ1ZVxuICAgIH1cblxuICAgIHJ1bkFjdGlvbih7XG4gICAgICBhY3Rpb25RdWV1ZSxcbiAgICAgIGFjdGlvbjogbmV3QWN0aW9uLFxuICAgICAgc2V0U3RhdGUsXG4gICAgfSlcbiAgfSBlbHNlIHtcbiAgICAvLyBUaGUgcXVldWUgaXMgbm90IGVtcHR5LCBzbyBhZGQgdGhlIGFjdGlvbiB0byB0aGUgZW5kIG9mIHRoZSBxdWV1ZVxuICAgIC8vIEl0IHdpbGwgYmUgc3RhcnRlZCBieSBydW5SZW1haW5pbmdBY3Rpb25zIGFmdGVyIHRoZSBwcmV2aW91cyBhY3Rpb24gZmluaXNoZXNcbiAgICBpZiAoYWN0aW9uUXVldWUubGFzdCAhPT0gbnVsbCkge1xuICAgICAgYWN0aW9uUXVldWUubGFzdC5uZXh0ID0gbmV3QWN0aW9uXG4gICAgfVxuICAgIGFjdGlvblF1ZXVlLmxhc3QgPSBuZXdBY3Rpb25cbiAgfVxufVxuXG5sZXQgZ2xvYmFsQWN0aW9uUXVldWU6IEFwcFJvdXRlckFjdGlvblF1ZXVlIHwgbnVsbCA9IG51bGxcblxuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZU11dGFibGVBY3Rpb25RdWV1ZShcbiAgaW5pdGlhbFN0YXRlOiBBcHBSb3V0ZXJTdGF0ZSxcbiAgaW5zdHJ1bWVudGF0aW9uSG9va3M6IENsaWVudEluc3RydW1lbnRhdGlvbkhvb2tzIHwgbnVsbFxuKTogQXBwUm91dGVyQWN0aW9uUXVldWUge1xuICBjb25zdCBhY3Rpb25RdWV1ZTogQXBwUm91dGVyQWN0aW9uUXVldWUgPSB7XG4gICAgc3RhdGU6IGluaXRpYWxTdGF0ZSxcbiAgICBkaXNwYXRjaDogKHBheWxvYWQ6IFJlZHVjZXJBY3Rpb25zLCBzZXRTdGF0ZTogRGlzcGF0Y2hTdGF0ZVByb21pc2UpID0+XG4gICAgICBkaXNwYXRjaEFjdGlvbihhY3Rpb25RdWV1ZSwgcGF5bG9hZCwgc2V0U3RhdGUpLFxuICAgIGFjdGlvbjogYXN5bmMgKHN0YXRlOiBBcHBSb3V0ZXJTdGF0ZSwgYWN0aW9uOiBSZWR1Y2VyQWN0aW9ucykgPT4ge1xuICAgICAgY29uc3QgcmVzdWx0ID0gcmVkdWNlcihzdGF0ZSwgYWN0aW9uKVxuICAgICAgcmV0dXJuIHJlc3VsdFxuICAgIH0sXG4gICAgcGVuZGluZzogbnVsbCxcbiAgICBsYXN0OiBudWxsLFxuICAgIG9uUm91dGVyVHJhbnNpdGlvblN0YXJ0OlxuICAgICAgaW5zdHJ1bWVudGF0aW9uSG9va3MgIT09IG51bGwgJiZcbiAgICAgIHR5cGVvZiBpbnN0cnVtZW50YXRpb25Ib29rcy5vblJvdXRlclRyYW5zaXRpb25TdGFydCA9PT0gJ2Z1bmN0aW9uJ1xuICAgICAgICA/IC8vIFRoaXMgcHJvZmlsaW5nIGhvb2sgd2lsbCBiZSBjYWxsZWQgYXQgdGhlIHN0YXJ0IG9mIGV2ZXJ5IG5hdmlnYXRpb24uXG4gICAgICAgICAgaW5zdHJ1bWVudGF0aW9uSG9va3Mub25Sb3V0ZXJUcmFuc2l0aW9uU3RhcnRcbiAgICAgICAgOiBudWxsLFxuICB9XG5cbiAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgLy8gVGhlIGFjdGlvbiBxdWV1ZSBpcyBsYXppbHkgY3JlYXRlZCBvbiBoeWRyYXRpb24sIGJ1dCBhZnRlciB0aGF0IHBvaW50XG4gICAgLy8gaXQgZG9lc24ndCBjaGFuZ2UuIFNvIHdlIGNhbiBzdG9yZSBpdCBpbiBhIGdsb2JhbCByYXRoZXIgdGhhbiBwYXNzXG4gICAgLy8gaXQgYXJvdW5kIGV2ZXJ5d2hlcmUgdmlhIHByb3BzL2NvbnRleHQuXG4gICAgaWYgKGdsb2JhbEFjdGlvblF1ZXVlICE9PSBudWxsKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgICdJbnRlcm5hbCBOZXh0LmpzIEVycm9yOiBjcmVhdGVNdXRhYmxlQWN0aW9uUXVldWUgd2FzIGNhbGxlZCBtb3JlICcgK1xuICAgICAgICAgICd0aGFuIG9uY2UnXG4gICAgICApXG4gICAgfVxuICAgIGdsb2JhbEFjdGlvblF1ZXVlID0gYWN0aW9uUXVldWVcbiAgfVxuXG4gIHJldHVybiBhY3Rpb25RdWV1ZVxufVxuXG5leHBvcnQgZnVuY3Rpb24gZ2V0Q3VycmVudEFwcFJvdXRlclN0YXRlKCk6IEFwcFJvdXRlclN0YXRlIHwgbnVsbCB7XG4gIHJldHVybiBnbG9iYWxBY3Rpb25RdWV1ZSAhPT0gbnVsbCA/IGdsb2JhbEFjdGlvblF1ZXVlLnN0YXRlIDogbnVsbFxufVxuXG5mdW5jdGlvbiBnZXRBcHBSb3V0ZXJBY3Rpb25RdWV1ZSgpOiBBcHBSb3V0ZXJBY3Rpb25RdWV1ZSB7XG4gIGlmIChnbG9iYWxBY3Rpb25RdWV1ZSA9PT0gbnVsbCkge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICdJbnRlcm5hbCBOZXh0LmpzIGVycm9yOiBSb3V0ZXIgYWN0aW9uIGRpc3BhdGNoZWQgYmVmb3JlIGluaXRpYWxpemF0aW9uLidcbiAgICApXG4gIH1cbiAgcmV0dXJuIGdsb2JhbEFjdGlvblF1ZXVlXG59XG5cbmZ1bmN0aW9uIGdldFByb2ZpbGluZ0hvb2tGb3JPbk5hdmlnYXRpb25TdGFydCgpIHtcbiAgaWYgKGdsb2JhbEFjdGlvblF1ZXVlICE9PSBudWxsKSB7XG4gICAgcmV0dXJuIGdsb2JhbEFjdGlvblF1ZXVlLm9uUm91dGVyVHJhbnNpdGlvblN0YXJ0XG4gIH1cbiAgcmV0dXJuIG51bGxcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGRpc3BhdGNoTmF2aWdhdGVBY3Rpb24oXG4gIGhyZWY6IHN0cmluZyxcbiAgbmF2aWdhdGVUeXBlOiBOYXZpZ2F0ZUFjdGlvblsnbmF2aWdhdGVUeXBlJ10sXG4gIHNob3VsZFNjcm9sbDogYm9vbGVhbixcbiAgbGlua0luc3RhbmNlUmVmOiBMaW5rSW5zdGFuY2UgfCBudWxsXG4pOiB2b2lkIHtcbiAgLy8gVE9ETzogVGhpcyBzdHVmZiBjb3VsZCBqdXN0IGdvIGludG8gdGhlIHJlZHVjZXIuIExlYXZpbmcgYXMtaXMgZm9yIG5vd1xuICAvLyBzaW5jZSB3ZSdyZSBhYm91dCB0byByZXdyaXRlIGFsbCB0aGUgcm91dGVyIHJlZHVjZXIgc3R1ZmYgYW55d2F5LlxuICBjb25zdCB1cmwgPSBuZXcgVVJMKGFkZEJhc2VQYXRoKGhyZWYpLCBsb2NhdGlvbi5ocmVmKVxuICBpZiAocHJvY2Vzcy5lbnYuX19ORVhUX0FQUF9OQVZfRkFJTF9IQU5ETElORykge1xuICAgIHdpbmRvdy5uZXh0Ll9fcGVuZGluZ1VybCA9IHVybFxuICB9XG5cbiAgc2V0TGlua0ZvckN1cnJlbnROYXZpZ2F0aW9uKGxpbmtJbnN0YW5jZVJlZilcblxuICBjb25zdCBvblJvdXRlclRyYW5zaXRpb25TdGFydCA9IGdldFByb2ZpbGluZ0hvb2tGb3JPbk5hdmlnYXRpb25TdGFydCgpXG4gIGlmIChvblJvdXRlclRyYW5zaXRpb25TdGFydCAhPT0gbnVsbCkge1xuICAgIG9uUm91dGVyVHJhbnNpdGlvblN0YXJ0KGhyZWYsIG5hdmlnYXRlVHlwZSlcbiAgfVxuXG4gIGRpc3BhdGNoQXBwUm91dGVyQWN0aW9uKHtcbiAgICB0eXBlOiBBQ1RJT05fTkFWSUdBVEUsXG4gICAgdXJsLFxuICAgIGlzRXh0ZXJuYWxVcmw6IGlzRXh0ZXJuYWxVUkwodXJsKSxcbiAgICBsb2NhdGlvblNlYXJjaDogbG9jYXRpb24uc2VhcmNoLFxuICAgIHNob3VsZFNjcm9sbCxcbiAgICBuYXZpZ2F0ZVR5cGUsXG4gICAgYWxsb3dBbGlhc2luZzogdHJ1ZSxcbiAgfSlcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGRpc3BhdGNoVHJhdmVyc2VBY3Rpb24oXG4gIGhyZWY6IHN0cmluZyxcbiAgdHJlZTogRmxpZ2h0Um91dGVyU3RhdGUgfCB1bmRlZmluZWRcbikge1xuICBjb25zdCBvblJvdXRlclRyYW5zaXRpb25TdGFydCA9IGdldFByb2ZpbGluZ0hvb2tGb3JPbk5hdmlnYXRpb25TdGFydCgpXG4gIGlmIChvblJvdXRlclRyYW5zaXRpb25TdGFydCAhPT0gbnVsbCkge1xuICAgIG9uUm91dGVyVHJhbnNpdGlvblN0YXJ0KGhyZWYsICd0cmF2ZXJzZScpXG4gIH1cbiAgZGlzcGF0Y2hBcHBSb3V0ZXJBY3Rpb24oe1xuICAgIHR5cGU6IEFDVElPTl9SRVNUT1JFLFxuICAgIHVybDogbmV3IFVSTChocmVmKSxcbiAgICB0cmVlLFxuICB9KVxufVxuXG4vKipcbiAqIFRoZSBhcHAgcm91dGVyIHRoYXQgaXMgZXhwb3NlZCB0aHJvdWdoIGB1c2VSb3V0ZXJgLiBUaGVzZSBhcmUgcHVibGljIEFQSVxuICogbWV0aG9kcy4gSW50ZXJuYWwgTmV4dC5qcyBjb2RlIHNob3VsZCBjYWxsIHRoZSBsb3dlciBsZXZlbCBtZXRob2RzIGRpcmVjdGx5XG4gKiAoYWx0aG91Z2ggdGhlcmUncyBsb3RzIG9mIGV4aXN0aW5nIGNvZGUgdGhhdCBkb2Vzbid0IGRvIHRoYXQpLlxuICovXG5leHBvcnQgY29uc3QgcHVibGljQXBwUm91dGVySW5zdGFuY2U6IEFwcFJvdXRlckluc3RhbmNlID0ge1xuICBiYWNrOiAoKSA9PiB3aW5kb3cuaGlzdG9yeS5iYWNrKCksXG4gIGZvcndhcmQ6ICgpID0+IHdpbmRvdy5oaXN0b3J5LmZvcndhcmQoKSxcbiAgcHJlZmV0Y2g6IHByb2Nlc3MuZW52Ll9fTkVYVF9DTElFTlRfU0VHTUVOVF9DQUNIRVxuICAgID8gLy8gVW5saWtlIHRoZSBvbGQgaW1wbGVtZW50YXRpb24sIHRoZSBTZWdtZW50IENhY2hlIGRvZXNuJ3Qgc3RvcmUgaXRzXG4gICAgICAvLyBkYXRhIGluIHRoZSByb3V0ZXIgcmVkdWNlciBzdGF0ZTsgaXQgd3JpdGVzIGludG8gYSBnbG9iYWwgbXV0YWJsZVxuICAgICAgLy8gY2FjaGUuIFNvIHdlIGRvbid0IG5lZWQgdG8gZGlzcGF0Y2ggYW4gYWN0aW9uLlxuICAgICAgKGhyZWY6IHN0cmluZywgb3B0aW9ucz86IFByZWZldGNoT3B0aW9ucykgPT4ge1xuICAgICAgICBjb25zdCBhY3Rpb25RdWV1ZSA9IGdldEFwcFJvdXRlckFjdGlvblF1ZXVlKClcbiAgICAgICAgcHJlZmV0Y2hXaXRoU2VnbWVudENhY2hlKFxuICAgICAgICAgIGhyZWYsXG4gICAgICAgICAgYWN0aW9uUXVldWUuc3RhdGUubmV4dFVybCxcbiAgICAgICAgICBhY3Rpb25RdWV1ZS5zdGF0ZS50cmVlLFxuICAgICAgICAgIG9wdGlvbnM/LmtpbmQgPT09IFByZWZldGNoS2luZC5GVUxMXG4gICAgICAgIClcbiAgICAgIH1cbiAgICA6IChocmVmOiBzdHJpbmcsIG9wdGlvbnM/OiBQcmVmZXRjaE9wdGlvbnMpID0+IHtcbiAgICAgICAgLy8gVXNlIHRoZSBvbGQgcHJlZmV0Y2ggaW1wbGVtZW50YXRpb24uXG4gICAgICAgIGNvbnN0IGFjdGlvblF1ZXVlID0gZ2V0QXBwUm91dGVyQWN0aW9uUXVldWUoKVxuICAgICAgICBjb25zdCB1cmwgPSBjcmVhdGVQcmVmZXRjaFVSTChocmVmKVxuICAgICAgICBpZiAodXJsICE9PSBudWxsKSB7XG4gICAgICAgICAgLy8gVGhlIHByZWZldGNoIHJlZHVjZXIgZG9lc24ndCBhY3R1YWxseSB1cGRhdGUgYW55IHN0YXRlIG9yXG4gICAgICAgICAgLy8gdHJpZ2dlciBhIHJlcmVuZGVyLiBJdCBqdXN0IHdyaXRlcyB0byBhIG11dGFibGUgY2FjaGUuIFNvIHdlXG4gICAgICAgICAgLy8gc2hvdWxkbid0IGJvdGhlciBjYWxsaW5nIHNldFN0YXRlL2Rpc3BhdGNoOyB3ZSBjYW4ganVzdCByZS1ydW5cbiAgICAgICAgICAvLyB0aGUgcmVkdWNlciBkaXJlY3RseSB1c2luZyB0aGUgY3VycmVudCBzdGF0ZS5cbiAgICAgICAgICAvLyBUT0RPOiBSZWZhY3RvciB0aGlzIGF3YXkgZnJvbSBhIFwicmVkdWNlclwiIHNvIGl0J3NcbiAgICAgICAgICAvLyBsZXNzIGNvbmZ1c2luZy5cbiAgICAgICAgICBwcmVmZXRjaFJlZHVjZXIoYWN0aW9uUXVldWUuc3RhdGUsIHtcbiAgICAgICAgICAgIHR5cGU6IEFDVElPTl9QUkVGRVRDSCxcbiAgICAgICAgICAgIHVybCxcbiAgICAgICAgICAgIGtpbmQ6IG9wdGlvbnM/LmtpbmQgPz8gUHJlZmV0Y2hLaW5kLkZVTEwsXG4gICAgICAgICAgfSlcbiAgICAgICAgfVxuICAgICAgfSxcbiAgcmVwbGFjZTogKGhyZWY6IHN0cmluZywgb3B0aW9ucz86IE5hdmlnYXRlT3B0aW9ucykgPT4ge1xuICAgIHN0YXJ0VHJhbnNpdGlvbigoKSA9PiB7XG4gICAgICBkaXNwYXRjaE5hdmlnYXRlQWN0aW9uKGhyZWYsICdyZXBsYWNlJywgb3B0aW9ucz8uc2Nyb2xsID8/IHRydWUsIG51bGwpXG4gICAgfSlcbiAgfSxcbiAgcHVzaDogKGhyZWY6IHN0cmluZywgb3B0aW9ucz86IE5hdmlnYXRlT3B0aW9ucykgPT4ge1xuICAgIHN0YXJ0VHJhbnNpdGlvbigoKSA9PiB7XG4gICAgICBkaXNwYXRjaE5hdmlnYXRlQWN0aW9uKGhyZWYsICdwdXNoJywgb3B0aW9ucz8uc2Nyb2xsID8/IHRydWUsIG51bGwpXG4gICAgfSlcbiAgfSxcbiAgcmVmcmVzaDogKCkgPT4ge1xuICAgIHN0YXJ0VHJhbnNpdGlvbigoKSA9PiB7XG4gICAgICBkaXNwYXRjaEFwcFJvdXRlckFjdGlvbih7XG4gICAgICAgIHR5cGU6IEFDVElPTl9SRUZSRVNILFxuICAgICAgICBvcmlnaW46IHdpbmRvdy5sb2NhdGlvbi5vcmlnaW4sXG4gICAgICB9KVxuICAgIH0pXG4gIH0sXG4gIGhtclJlZnJlc2g6ICgpID0+IHtcbiAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdkZXZlbG9wbWVudCcpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICAgJ2htclJlZnJlc2ggY2FuIG9ubHkgYmUgdXNlZCBpbiBkZXZlbG9wbWVudCBtb2RlLiBQbGVhc2UgdXNlIHJlZnJlc2ggaW5zdGVhZC4nXG4gICAgICApXG4gICAgfSBlbHNlIHtcbiAgICAgIHN0YXJ0VHJhbnNpdGlvbigoKSA9PiB7XG4gICAgICAgIGRpc3BhdGNoQXBwUm91dGVyQWN0aW9uKHtcbiAgICAgICAgICB0eXBlOiBBQ1RJT05fSE1SX1JFRlJFU0gsXG4gICAgICAgICAgb3JpZ2luOiB3aW5kb3cubG9jYXRpb24ub3JpZ2luLFxuICAgICAgICB9KVxuICAgICAgfSlcbiAgICB9XG4gIH0sXG59XG5cbi8vIEV4aXN0cyBmb3IgZGVidWdnaW5nIHB1cnBvc2VzLiBEb24ndCB1c2UgaW4gYXBwbGljYXRpb24gY29kZS5cbmlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiB3aW5kb3cubmV4dCkge1xuICB3aW5kb3cubmV4dC5yb3V0ZXIgPSBwdWJsaWNBcHBSb3V0ZXJJbnN0YW5jZVxufVxuIl0sIm5hbWVzIjpbImNyZWF0ZU11dGFibGVBY3Rpb25RdWV1ZSIsImRpc3BhdGNoTmF2aWdhdGVBY3Rpb24iLCJkaXNwYXRjaFRyYXZlcnNlQWN0aW9uIiwiZ2V0Q3VycmVudEFwcFJvdXRlclN0YXRlIiwicHVibGljQXBwUm91dGVySW5zdGFuY2UiLCJydW5SZW1haW5pbmdBY3Rpb25zIiwiYWN0aW9uUXVldWUiLCJzZXRTdGF0ZSIsInBlbmRpbmciLCJuZXh0IiwicnVuQWN0aW9uIiwiYWN0aW9uIiwibmVlZHNSZWZyZXNoIiwiZGlzcGF0Y2giLCJ0eXBlIiwiQUNUSU9OX1JFRlJFU0giLCJvcmlnaW4iLCJ3aW5kb3ciLCJsb2NhdGlvbiIsInByZXZTdGF0ZSIsInN0YXRlIiwicGF5bG9hZCIsImFjdGlvblJlc3VsdCIsImhhbmRsZVJlc3VsdCIsIm5leHRTdGF0ZSIsImRpc2NhcmRlZCIsInJlc29sdmUiLCJpc1RoZW5hYmxlIiwidGhlbiIsImVyciIsInJlamVjdCIsImRpc3BhdGNoQWN0aW9uIiwicmVzb2x2ZXJzIiwiQUNUSU9OX1JFU1RPUkUiLCJkZWZlcnJlZFByb21pc2UiLCJQcm9taXNlIiwic3RhcnRUcmFuc2l0aW9uIiwibmV3QWN0aW9uIiwibGFzdCIsIkFDVElPTl9OQVZJR0FURSIsIkFDVElPTl9TRVJWRVJfQUNUSU9OIiwiZ2xvYmFsQWN0aW9uUXVldWUiLCJpbml0aWFsU3RhdGUiLCJpbnN0cnVtZW50YXRpb25Ib29rcyIsInJlc3VsdCIsInJlZHVjZXIiLCJvblJvdXRlclRyYW5zaXRpb25TdGFydCIsIkVycm9yIiwiZ2V0QXBwUm91dGVyQWN0aW9uUXVldWUiLCJnZXRQcm9maWxpbmdIb29rRm9yT25OYXZpZ2F0aW9uU3RhcnQiLCJocmVmIiwibmF2aWdhdGVUeXBlIiwic2hvdWxkU2Nyb2xsIiwibGlua0luc3RhbmNlUmVmIiwidXJsIiwiVVJMIiwiYWRkQmFzZVBhdGgiLCJwcm9jZXNzIiwiZW52IiwiX19ORVhUX0FQUF9OQVZfRkFJTF9IQU5ETElORyIsIl9fcGVuZGluZ1VybCIsInNldExpbmtGb3JDdXJyZW50TmF2aWdhdGlvbiIsImRpc3BhdGNoQXBwUm91dGVyQWN0aW9uIiwiaXNFeHRlcm5hbFVybCIsImlzRXh0ZXJuYWxVUkwiLCJsb2NhdGlvblNlYXJjaCIsInNlYXJjaCIsImFsbG93QWxpYXNpbmciLCJ0cmVlIiwiYmFjayIsImhpc3RvcnkiLCJmb3J3YXJkIiwicHJlZmV0Y2giLCJfX05FWFRfQ0xJRU5UX1NFR01FTlRfQ0FDSEUiLCJvcHRpb25zIiwicHJlZmV0Y2hXaXRoU2VnbWVudENhY2hlIiwibmV4dFVybCIsImtpbmQiLCJQcmVmZXRjaEtpbmQiLCJGVUxMIiwiY3JlYXRlUHJlZmV0Y2hVUkwiLCJwcmVmZXRjaFJlZHVjZXIiLCJBQ1RJT05fUFJFRkVUQ0giLCJyZXBsYWNlIiwic2Nyb2xsIiwicHVzaCIsInJlZnJlc2giLCJobXJSZWZyZXNoIiwiTk9ERV9FTlYiLCJBQ1RJT05fSE1SX1JFRlJFU0giLCJyb3V0ZXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":
/*!****************************************************************!*\
  !*** ./node_modules/next/dist/client/components/app-router.js ***!
  \****************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createEmptyCacheNode: function() {\n        return createEmptyCacheNode;\n    },\n    createPrefetchURL: function() {\n        return createPrefetchURL;\n    },\n    default: function() {\n        return AppRouter;\n    },\n    isExternalURL: function() {\n        return isExternalURL;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _createhreffromurl = __webpack_require__(/*! ./router-reducer/create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _hooksclientcontextsharedruntime = __webpack_require__(/*! ../../shared/lib/hooks-client-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js\");\nconst _useactionqueue = __webpack_require__(/*! ./use-action-queue */ \"(app-pages-browser)/./node_modules/next/dist/client/components/use-action-queue.js\");\nconst _errorboundary = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ./error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\"));\nconst _isbot = __webpack_require__(/*! ../../shared/lib/router/utils/is-bot */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-bot.js\");\nconst _addbasepath = __webpack_require__(/*! ../add-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/add-base-path.js\");\nconst _approuterannouncer = __webpack_require__(/*! ./app-router-announcer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-announcer.js\");\nconst _redirectboundary = __webpack_require__(/*! ./redirect-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect-boundary.js\");\nconst _findheadincache = __webpack_require__(/*! ./router-reducer/reducers/find-head-in-cache */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/find-head-in-cache.js\");\nconst _unresolvedthenable = __webpack_require__(/*! ./unresolved-thenable */ \"(app-pages-browser)/./node_modules/next/dist/client/components/unresolved-thenable.js\");\nconst _removebasepath = __webpack_require__(/*! ../remove-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/remove-base-path.js\");\nconst _hasbasepath = __webpack_require__(/*! ../has-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/has-base-path.js\");\nconst _computechangedpath = __webpack_require__(/*! ./router-reducer/compute-changed-path */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/compute-changed-path.js\");\nconst _navfailurehandler = __webpack_require__(/*! ./nav-failure-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/nav-failure-handler.js\");\nconst _approuterinstance = __webpack_require__(/*! ./app-router-instance */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js\");\nconst _redirect = __webpack_require__(/*! ./redirect */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect.js\");\nconst _redirecterror = __webpack_require__(/*! ./redirect-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect-error.js\");\nconst _links = __webpack_require__(/*! ./links */ \"(app-pages-browser)/./node_modules/next/dist/client/components/links.js\");\nconst globalMutable = {};\nfunction isExternalURL(url) {\n    return url.origin !== window.location.origin;\n}\nfunction createPrefetchURL(href) {\n    // Don't prefetch for bots as they don't navigate.\n    if ((0, _isbot.isBot)(window.navigator.userAgent)) {\n        return null;\n    }\n    let url;\n    try {\n        url = new URL((0, _addbasepath.addBasePath)(href), window.location.href);\n    } catch (_) {\n        // TODO: Does this need to throw or can we just console.error instead? Does\n        // anyone rely on this throwing? (Seems unlikely.)\n        throw Object.defineProperty(new Error(\"Cannot prefetch '\" + href + \"' because it cannot be converted to a URL.\"), \"__NEXT_ERROR_CODE\", {\n            value: \"E234\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    // Don't prefetch during development (improves compilation performance)\n    if (true) {\n        return null;\n    }\n    // External urls can't be prefetched in the same way.\n    if (isExternalURL(url)) {\n        return null;\n    }\n    return url;\n}\nfunction HistoryUpdater(param) {\n    let { appRouterState } = param;\n    (0, _react.useInsertionEffect)(()=>{\n        if (false) {}\n        const { tree, pushRef, canonicalUrl } = appRouterState;\n        const historyState = {\n            ...pushRef.preserveCustomHistoryState ? window.history.state : {},\n            // Identifier is shortened intentionally.\n            // __NA is used to identify if the history entry can be handled by the app-router.\n            // __N is used to identify if the history entry can be handled by the old router.\n            __NA: true,\n            __PRIVATE_NEXTJS_INTERNALS_TREE: tree\n        };\n        if (pushRef.pendingPush && // Skip pushing an additional history entry if the canonicalUrl is the same as the current url.\n        // This mirrors the browser behavior for normal navigation.\n        (0, _createhreffromurl.createHrefFromUrl)(new URL(window.location.href)) !== canonicalUrl) {\n            // This intentionally mutates React state, pushRef is overwritten to ensure additional push/replace calls do not trigger an additional history entry.\n            pushRef.pendingPush = false;\n            window.history.pushState(historyState, '', canonicalUrl);\n        } else {\n            window.history.replaceState(historyState, '', canonicalUrl);\n        }\n    }, [\n        appRouterState\n    ]);\n    (0, _react.useEffect)(()=>{\n        // The Next-Url and the base tree may affect the result of a prefetch\n        // task. Re-prefetch all visible links with the updated values. In most\n        // cases, this will not result in any new network requests, only if\n        // the prefetch result actually varies on one of these inputs.\n        if (false) {}\n    }, [\n        appRouterState.nextUrl,\n        appRouterState.tree\n    ]);\n    return null;\n}\n_c = HistoryUpdater;\nfunction createEmptyCacheNode() {\n    return {\n        lazyData: null,\n        rsc: null,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading: null,\n        navigatedAt: -1\n    };\n}\nfunction copyNextJsInternalHistoryState(data) {\n    if (data == null) data = {};\n    const currentState = window.history.state;\n    const __NA = currentState == null ? void 0 : currentState.__NA;\n    if (__NA) {\n        data.__NA = __NA;\n    }\n    const __PRIVATE_NEXTJS_INTERNALS_TREE = currentState == null ? void 0 : currentState.__PRIVATE_NEXTJS_INTERNALS_TREE;\n    if (__PRIVATE_NEXTJS_INTERNALS_TREE) {\n        data.__PRIVATE_NEXTJS_INTERNALS_TREE = __PRIVATE_NEXTJS_INTERNALS_TREE;\n    }\n    return data;\n}\nfunction Head(param) {\n    let { headCacheNode } = param;\n    // If this segment has a `prefetchHead`, it's the statically prefetched data.\n    // We should use that on initial render instead of `head`. Then we'll switch\n    // to `head` when the dynamic response streams in.\n    const head = headCacheNode !== null ? headCacheNode.head : null;\n    const prefetchHead = headCacheNode !== null ? headCacheNode.prefetchHead : null;\n    // If no prefetch data is available, then we go straight to rendering `head`.\n    const resolvedPrefetchRsc = prefetchHead !== null ? prefetchHead : head;\n    // We use `useDeferredValue` to handle switching between the prefetched and\n    // final values. The second argument is returned on initial render, then it\n    // re-renders with the first argument.\n    return (0, _react.useDeferredValue)(head, resolvedPrefetchRsc);\n}\n_c1 = Head;\n/**\n * The global router that wraps the application components.\n */ function Router(param) {\n    let { actionQueue, assetPrefix, globalError } = param;\n    const state = (0, _useactionqueue.useActionQueue)(actionQueue);\n    const { canonicalUrl } = state;\n    // Add memoized pathname/query for useSearchParams and usePathname.\n    const { searchParams, pathname } = (0, _react.useMemo)(()=>{\n        const url = new URL(canonicalUrl,  false ? 0 : window.location.href);\n        return {\n            // This is turned into a readonly class in `useSearchParams`\n            searchParams: url.searchParams,\n            pathname: (0, _hasbasepath.hasBasePath)(url.pathname) ? (0, _removebasepath.removeBasePath)(url.pathname) : url.pathname\n        };\n    }, [\n        canonicalUrl\n    ]);\n    if (true) {\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const { cache, prefetchCache, tree } = state;\n        // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        (0, _react.useEffect)(()=>{\n            // Add `window.nd` for debugging purposes.\n            // This is not meant for use in applications as concurrent rendering will affect the cache/tree/router.\n            // @ts-ignore this is for debugging\n            window.nd = {\n                router: _approuterinstance.publicAppRouterInstance,\n                cache,\n                prefetchCache,\n                tree\n            };\n        }, [\n            cache,\n            prefetchCache,\n            tree\n        ]);\n    }\n    (0, _react.useEffect)(()=>{\n        // If the app is restored from bfcache, it's possible that\n        // pushRef.mpaNavigation is true, which would mean that any re-render of this component\n        // would trigger the mpa navigation logic again from the lines below.\n        // This will restore the router to the initial state in the event that the app is restored from bfcache.\n        function handlePageShow(event) {\n            var _window_history_state;\n            if (!event.persisted || !((_window_history_state = window.history.state) == null ? void 0 : _window_history_state.__PRIVATE_NEXTJS_INTERNALS_TREE)) {\n                return;\n            }\n            // Clear the pendingMpaPath value so that a subsequent MPA navigation to the same URL can be triggered.\n            // This is necessary because if the browser restored from bfcache, the pendingMpaPath would still be set to the value\n            // of the last MPA navigation.\n            globalMutable.pendingMpaPath = undefined;\n            (0, _useactionqueue.dispatchAppRouterAction)({\n                type: _routerreducertypes.ACTION_RESTORE,\n                url: new URL(window.location.href),\n                tree: window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE\n            });\n        }\n        window.addEventListener('pageshow', handlePageShow);\n        return ()=>{\n            window.removeEventListener('pageshow', handlePageShow);\n        };\n    }, []);\n    (0, _react.useEffect)(()=>{\n        // Ensure that any redirect errors that bubble up outside of the RedirectBoundary\n        // are caught and handled by the router.\n        function handleUnhandledRedirect(event) {\n            const error = 'reason' in event ? event.reason : event.error;\n            if ((0, _redirecterror.isRedirectError)(error)) {\n                event.preventDefault();\n                const url = (0, _redirect.getURLFromRedirectError)(error);\n                const redirectType = (0, _redirect.getRedirectTypeFromError)(error);\n                // TODO: This should access the router methods directly, rather than\n                // go through the public interface.\n                if (redirectType === _redirecterror.RedirectType.push) {\n                    _approuterinstance.publicAppRouterInstance.push(url, {});\n                } else {\n                    _approuterinstance.publicAppRouterInstance.replace(url, {});\n                }\n            }\n        }\n        window.addEventListener('error', handleUnhandledRedirect);\n        window.addEventListener('unhandledrejection', handleUnhandledRedirect);\n        return ()=>{\n            window.removeEventListener('error', handleUnhandledRedirect);\n            window.removeEventListener('unhandledrejection', handleUnhandledRedirect);\n        };\n    }, []);\n    // When mpaNavigation flag is set do a hard navigation to the new url.\n    // Infinitely suspend because we don't actually want to rerender any child\n    // components with the new URL and any entangled state updates shouldn't\n    // commit either (eg: useTransition isPending should stay true until the page\n    // unloads).\n    //\n    // This is a side effect in render. Don't try this at home, kids. It's\n    // probably safe because we know this is a singleton component and it's never\n    // in <Offscreen>. At least I hope so. (It will run twice in dev strict mode,\n    // but that's... fine?)\n    const { pushRef } = state;\n    if (pushRef.mpaNavigation) {\n        // if there's a re-render, we don't want to trigger another redirect if one is already in flight to the same URL\n        if (globalMutable.pendingMpaPath !== canonicalUrl) {\n            const location = window.location;\n            if (pushRef.pendingPush) {\n                location.assign(canonicalUrl);\n            } else {\n                location.replace(canonicalUrl);\n            }\n            globalMutable.pendingMpaPath = canonicalUrl;\n        }\n        // TODO-APP: Should we listen to navigateerror here to catch failed\n        // navigations somehow? And should we call window.stop() if a SPA navigation\n        // should interrupt an MPA one?\n        (0, _react.use)(_unresolvedthenable.unresolvedThenable);\n    }\n    (0, _react.useEffect)(()=>{\n        const originalPushState = window.history.pushState.bind(window.history);\n        const originalReplaceState = window.history.replaceState.bind(window.history);\n        // Ensure the canonical URL in the Next.js Router is updated when the URL is changed so that `usePathname` and `useSearchParams` hold the pushed values.\n        const applyUrlFromHistoryPushReplace = (url)=>{\n            var _window_history_state;\n            const href = window.location.href;\n            const tree = (_window_history_state = window.history.state) == null ? void 0 : _window_history_state.__PRIVATE_NEXTJS_INTERNALS_TREE;\n            (0, _react.startTransition)(()=>{\n                (0, _useactionqueue.dispatchAppRouterAction)({\n                    type: _routerreducertypes.ACTION_RESTORE,\n                    url: new URL(url != null ? url : href, href),\n                    tree\n                });\n            });\n        };\n        /**\n     * Patch pushState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */ window.history.pushState = function pushState(data, _unused, url) {\n            // Avoid a loop when Next.js internals trigger pushState/replaceState\n            if ((data == null ? void 0 : data.__NA) || (data == null ? void 0 : data._N)) {\n                return originalPushState(data, _unused, url);\n            }\n            data = copyNextJsInternalHistoryState(data);\n            if (url) {\n                applyUrlFromHistoryPushReplace(url);\n            }\n            return originalPushState(data, _unused, url);\n        };\n        /**\n     * Patch replaceState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */ window.history.replaceState = function replaceState(data, _unused, url) {\n            // Avoid a loop when Next.js internals trigger pushState/replaceState\n            if ((data == null ? void 0 : data.__NA) || (data == null ? void 0 : data._N)) {\n                return originalReplaceState(data, _unused, url);\n            }\n            data = copyNextJsInternalHistoryState(data);\n            if (url) {\n                applyUrlFromHistoryPushReplace(url);\n            }\n            return originalReplaceState(data, _unused, url);\n        };\n        /**\n     * Handle popstate event, this is used to handle back/forward in the browser.\n     * By default dispatches ACTION_RESTORE, however if the history entry was not pushed/replaced by app-router it will reload the page.\n     * That case can happen when the old router injected the history entry.\n     */ const onPopState = (event)=>{\n            if (!event.state) {\n                // TODO-APP: this case only happens when pushState/replaceState was called outside of Next.js. It should probably reload the page in this case.\n                return;\n            }\n            // This case happens when the history entry was pushed by the `pages` router.\n            if (!event.state.__NA) {\n                window.location.reload();\n                return;\n            }\n            // TODO-APP: Ideally the back button should not use startTransition as it should apply the updates synchronously\n            // Without startTransition works if the cache is there for this path\n            (0, _react.startTransition)(()=>{\n                (0, _approuterinstance.dispatchTraverseAction)(window.location.href, event.state.__PRIVATE_NEXTJS_INTERNALS_TREE);\n            });\n        };\n        // Register popstate event to call onPopstate.\n        window.addEventListener('popstate', onPopState);\n        return ()=>{\n            window.history.pushState = originalPushState;\n            window.history.replaceState = originalReplaceState;\n            window.removeEventListener('popstate', onPopState);\n        };\n    }, []);\n    const { cache, tree, nextUrl, focusAndScrollRef } = state;\n    const matchingHead = (0, _react.useMemo)(()=>{\n        return (0, _findheadincache.findHeadInCache)(cache, tree[1]);\n    }, [\n        cache,\n        tree\n    ]);\n    // Add memoized pathParams for useParams.\n    const pathParams = (0, _react.useMemo)(()=>{\n        return (0, _computechangedpath.getSelectedParams)(tree);\n    }, [\n        tree\n    ]);\n    const layoutRouterContext = (0, _react.useMemo)(()=>{\n        return {\n            parentTree: tree,\n            parentCacheNode: cache,\n            parentSegmentPath: null,\n            // Root node always has `url`\n            // Provided in AppTreeContext to ensure it can be overwritten in layout-router\n            url: canonicalUrl\n        };\n    }, [\n        tree,\n        cache,\n        canonicalUrl\n    ]);\n    const globalLayoutRouterContext = (0, _react.useMemo)(()=>{\n        return {\n            tree,\n            focusAndScrollRef,\n            nextUrl\n        };\n    }, [\n        tree,\n        focusAndScrollRef,\n        nextUrl\n    ]);\n    let head;\n    if (matchingHead !== null) {\n        // The head is wrapped in an extra component so we can use\n        // `useDeferredValue` to swap between the prefetched and final versions of\n        // the head. (This is what LayoutRouter does for segment data, too.)\n        //\n        // The `key` is used to remount the component whenever the head moves to\n        // a different segment.\n        const [headCacheNode, headKey] = matchingHead;\n        head = /*#__PURE__*/ (0, _jsxruntime.jsx)(Head, {\n            headCacheNode: headCacheNode\n        }, headKey);\n    } else {\n        head = null;\n    }\n    let content = /*#__PURE__*/ (0, _jsxruntime.jsxs)(_redirectboundary.RedirectBoundary, {\n        children: [\n            head,\n            cache.rsc,\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_approuterannouncer.AppRouterAnnouncer, {\n                tree: tree\n            })\n        ]\n    });\n    if (true) {\n        // In development, we apply few error boundaries and hot-reloader:\n        // - DevRootHTTPAccessFallbackBoundary: avoid using navigation API like notFound() in root layout\n        // - HotReloader:\n        //  - hot-reload the app when the code changes\n        //  - render dev overlay\n        //  - catch runtime errors and display global-error when necessary\n        if (true) {\n            const { DevRootHTTPAccessFallbackBoundary } = __webpack_require__(/*! ./dev-root-http-access-fallback-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/dev-root-http-access-fallback-boundary.js\");\n            content = /*#__PURE__*/ (0, _jsxruntime.jsx)(DevRootHTTPAccessFallbackBoundary, {\n                children: content\n            });\n        }\n        const HotReloader = (__webpack_require__(/*! ./react-dev-overlay/app/hot-reloader-client */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js\")[\"default\"]);\n        content = /*#__PURE__*/ (0, _jsxruntime.jsx)(HotReloader, {\n            assetPrefix: assetPrefix,\n            globalError: globalError,\n            children: content\n        });\n    } else {}\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(HistoryUpdater, {\n                appRouterState: state\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(RuntimeStyles, {}),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.PathParamsContext.Provider, {\n                value: pathParams,\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.PathnameContext.Provider, {\n                    value: pathname,\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.SearchParamsContext.Provider, {\n                        value: searchParams,\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.GlobalLayoutRouterContext.Provider, {\n                            value: globalLayoutRouterContext,\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.AppRouterContext.Provider, {\n                                value: _approuterinstance.publicAppRouterInstance,\n                                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.LayoutRouterContext.Provider, {\n                                    value: layoutRouterContext,\n                                    children: content\n                                })\n                            })\n                        })\n                    })\n                })\n            })\n        ]\n    });\n}\n_c2 = Router;\nfunction AppRouter(param) {\n    let { actionQueue, globalErrorComponentAndStyles: [globalErrorComponent, globalErrorStyles], assetPrefix } = param;\n    (0, _navfailurehandler.useNavFailureHandler)();\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_errorboundary.ErrorBoundary, {\n        // At the very top level, use the default GlobalError component as the final fallback.\n        // When the app router itself fails, which means the framework itself fails, we show the default error.\n        errorComponent: _errorboundary.default,\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Router, {\n            actionQueue: actionQueue,\n            assetPrefix: assetPrefix,\n            globalError: [\n                globalErrorComponent,\n                globalErrorStyles\n            ]\n        })\n    });\n}\n_c3 = AppRouter;\nconst runtimeStyles = new Set();\nlet runtimeStyleChanged = new Set();\nglobalThis._N_E_STYLE_LOAD = function(href) {\n    let len = runtimeStyles.size;\n    runtimeStyles.add(href);\n    if (runtimeStyles.size !== len) {\n        runtimeStyleChanged.forEach((cb)=>cb());\n    }\n    // TODO figure out how to get a promise here\n    // But maybe it's not necessary as react would block rendering until it's loaded\n    return Promise.resolve();\n};\nfunction RuntimeStyles() {\n    _s();\n    const [, forceUpdate] = _react.default.useState(0);\n    const renderedStylesSize = runtimeStyles.size;\n    (0, _react.useEffect)(()=>{\n        const changed = ()=>forceUpdate((c)=>c + 1);\n        runtimeStyleChanged.add(changed);\n        if (renderedStylesSize !== runtimeStyles.size) {\n            changed();\n        }\n        return ()=>{\n            runtimeStyleChanged.delete(changed);\n        };\n    }, [\n        renderedStylesSize,\n        forceUpdate\n    ]);\n    const dplId =  false ? 0 : '';\n    return [\n        ...runtimeStyles\n    ].map((href, i)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n            rel: \"stylesheet\",\n            href: \"\" + href + dplId,\n            // @ts-ignore\n            precedence: \"next\"\n        }, i));\n}\n_s(RuntimeStyles, \"Eht7Kgdrrgt5B4LSklQ7qDPo8Aw=\");\n_c4 = RuntimeStyles;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-router.js.map\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"HistoryUpdater\");\n$RefreshReg$(_c1, \"Head\");\n$RefreshReg$(_c2, \"Router\");\n$RefreshReg$(_c3, \"AppRouter\");\n$RefreshReg$(_c4, \"RuntimeStyles\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/globals/intercept-console-error.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/globals/intercept-console-error.js ***!
  \*************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    originConsoleError: function() {\n        return originConsoleError;\n    },\n    patchConsoleError: function() {\n        return patchConsoleError;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _iserror = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../../lib/is-error */ \"(app-pages-browser)/./node_modules/next/dist/lib/is-error.js\"));\nconst _isnextroutererror = __webpack_require__(/*! ../is-next-router-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/is-next-router-error.js\");\nconst _useerrorhandler = __webpack_require__(/*! ../errors/use-error-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/use-error-handler.js\");\nconst _console = __webpack_require__(/*! ../../lib/console */ \"(app-pages-browser)/./node_modules/next/dist/client/lib/console.js\");\nconst originConsoleError = globalThis.console.error;\nfunction patchConsoleError() {\n    // Ensure it's only patched once\n    if (false) {}\n    window.console.error = function error() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        let maybeError;\n        if (true) {\n            const { error: replayedError } = (0, _console.parseConsoleArgs)(args);\n            if (replayedError) {\n                maybeError = replayedError;\n            } else if ((0, _iserror.default)(args[0])) {\n                maybeError = args[0];\n            } else {\n                // See https://github.com/facebook/react/blob/d50323eb845c5fde0d720cae888bf35dedd05506/packages/react-reconciler/src/ReactFiberErrorLogger.js#L78\n                maybeError = args[1];\n            }\n        } else {}\n        if (!(0, _isnextroutererror.isNextRouterError)(maybeError)) {\n            if (true) {\n                (0, _useerrorhandler.handleConsoleError)(// but if we pass the error directly, `handleClientError` will ignore it\n                maybeError, args);\n            }\n            originConsoleError.apply(window.console, args);\n        }\n    };\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=intercept-console-error.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/globals/intercept-console-error.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js ***!
  \*****************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"HTTPAccessFallbackBoundary\", ({\n    enumerable: true,\n    get: function() {\n        return HTTPAccessFallbackBoundary;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _navigationuntracked = __webpack_require__(/*! ../navigation-untracked */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation-untracked.js\");\nconst _httpaccessfallback = __webpack_require__(/*! ./http-access-fallback */ \"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/http-access-fallback.js\");\nconst _warnonce = __webpack_require__(/*! ../../../shared/lib/utils/warn-once */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nclass HTTPAccessFallbackErrorBoundary extends _react.default.Component {\n    componentDidCatch() {\n        if ( true && this.props.missingSlots && this.props.missingSlots.size > 0 && // A missing children slot is the typical not-found case, so no need to warn\n        !this.props.missingSlots.has('children')) {\n            let warningMessage = 'No default component was found for a parallel route rendered on this page. Falling back to nearest NotFound boundary.\\n' + 'Learn more: https://nextjs.org/docs/app/building-your-application/routing/parallel-routes#defaultjs\\n\\n';\n            const formattedSlots = Array.from(this.props.missingSlots).sort((a, b)=>a.localeCompare(b)).map((slot)=>\"@\" + slot).join(', ');\n            warningMessage += 'Missing slots: ' + formattedSlots;\n            (0, _warnonce.warnOnce)(warningMessage);\n        }\n    }\n    static getDerivedStateFromError(error) {\n        if ((0, _httpaccessfallback.isHTTPAccessFallbackError)(error)) {\n            const httpStatus = (0, _httpaccessfallback.getAccessFallbackHTTPStatus)(error);\n            return {\n                triggeredStatus: httpStatus\n            };\n        }\n        // Re-throw if error is not for 404\n        throw error;\n    }\n    static getDerivedStateFromProps(props, state) {\n        /**\n     * Handles reset of the error boundary when a navigation happens.\n     * Ensures the error boundary does not stay enabled when navigating to a new page.\n     * Approach of setState in render is safe as it checks the previous pathname and then overrides\n     * it as outlined in https://react.dev/reference/react/useState#storing-information-from-previous-renders\n     */ if (props.pathname !== state.previousPathname && state.triggeredStatus) {\n            return {\n                triggeredStatus: undefined,\n                previousPathname: props.pathname\n            };\n        }\n        return {\n            triggeredStatus: state.triggeredStatus,\n            previousPathname: props.pathname\n        };\n    }\n    render() {\n        const { notFound, forbidden, unauthorized, children } = this.props;\n        const { triggeredStatus } = this.state;\n        const errorComponents = {\n            [_httpaccessfallback.HTTPAccessErrorStatus.NOT_FOUND]: notFound,\n            [_httpaccessfallback.HTTPAccessErrorStatus.FORBIDDEN]: forbidden,\n            [_httpaccessfallback.HTTPAccessErrorStatus.UNAUTHORIZED]: unauthorized\n        };\n        if (triggeredStatus) {\n            const isNotFound = triggeredStatus === _httpaccessfallback.HTTPAccessErrorStatus.NOT_FOUND && notFound;\n            const isForbidden = triggeredStatus === _httpaccessfallback.HTTPAccessErrorStatus.FORBIDDEN && forbidden;\n            const isUnauthorized = triggeredStatus === _httpaccessfallback.HTTPAccessErrorStatus.UNAUTHORIZED && unauthorized;\n            // If there's no matched boundary in this layer, keep throwing the error by rendering the children\n            if (!(isNotFound || isForbidden || isUnauthorized)) {\n                return children;\n            }\n            return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n                        name: \"robots\",\n                        content: \"noindex\"\n                    }),\n                     true && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n                        name: \"boundary-next-error\",\n                        content: (0, _httpaccessfallback.getAccessFallbackErrorTypeByStatus)(triggeredStatus)\n                    }),\n                    errorComponents[triggeredStatus]\n                ]\n            });\n        }\n        return children;\n    }\n    constructor(props){\n        super(props);\n        this.state = {\n            triggeredStatus: undefined,\n            previousPathname: props.pathname\n        };\n    }\n}\nfunction HTTPAccessFallbackBoundary(param) {\n    let { notFound, forbidden, unauthorized, children } = param;\n    // When we're rendering the missing params shell, this will return null. This\n    // is because we won't be rendering any not found boundaries or error\n    // boundaries for the missing params shell. When this runs on the client\n    // (where these error can occur), we will get the correct pathname.\n    const pathname = (0, _navigationuntracked.useUntrackedPathname)();\n    const missingSlots = (0, _react.useContext)(_approutercontextsharedruntime.MissingSlotContext);\n    const hasErrorFallback = !!(notFound || forbidden || unauthorized);\n    if (hasErrorFallback) {\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(HTTPAccessFallbackErrorBoundary, {\n            pathname: pathname,\n            notFound: notFound,\n            forbidden: forbidden,\n            unauthorized: unauthorized,\n            missingSlots: missingSlots,\n            children: children\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n        children: children\n    });\n}\n_c = HTTPAccessFallbackBoundary;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=error-boundary.js.map\nvar _c;\n$RefreshReg$(_c, \"HTTPAccessFallbackBoundary\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/links.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/client/components/links.js ***!
  \***********************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    IDLE_LINK_STATUS: function() {\n        return IDLE_LINK_STATUS;\n    },\n    PENDING_LINK_STATUS: function() {\n        return PENDING_LINK_STATUS;\n    },\n    mountFormInstance: function() {\n        return mountFormInstance;\n    },\n    mountLinkInstance: function() {\n        return mountLinkInstance;\n    },\n    onLinkVisibilityChanged: function() {\n        return onLinkVisibilityChanged;\n    },\n    onNavigationIntent: function() {\n        return onNavigationIntent;\n    },\n    pingVisibleLinks: function() {\n        return pingVisibleLinks;\n    },\n    setLinkForCurrentNavigation: function() {\n        return setLinkForCurrentNavigation;\n    },\n    unmountLinkForCurrentNavigation: function() {\n        return unmountLinkForCurrentNavigation;\n    },\n    unmountPrefetchableInstance: function() {\n        return unmountPrefetchableInstance;\n    }\n});\nconst _approuterinstance = __webpack_require__(/*! ./app-router-instance */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js\");\nconst _approuter = __webpack_require__(/*! ./app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _segmentcache = __webpack_require__(/*! ./segment-cache */ \"(app-pages-browser)/./node_modules/next/dist/client/components/segment-cache.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n// Tracks the most recently navigated link instance. When null, indicates\n// the current navigation was not initiated by a link click.\nlet linkForMostRecentNavigation = null;\nconst PENDING_LINK_STATUS = {\n    pending: true\n};\nconst IDLE_LINK_STATUS = {\n    pending: false\n};\nfunction setLinkForCurrentNavigation(link) {\n    (0, _react.startTransition)(()=>{\n        linkForMostRecentNavigation == null ? void 0 : linkForMostRecentNavigation.setOptimisticLinkStatus(IDLE_LINK_STATUS);\n        link == null ? void 0 : link.setOptimisticLinkStatus(PENDING_LINK_STATUS);\n        linkForMostRecentNavigation = link;\n    });\n}\nfunction unmountLinkForCurrentNavigation(link) {\n    if (linkForMostRecentNavigation === link) {\n        linkForMostRecentNavigation = null;\n    }\n}\n// Use a WeakMap to associate a Link instance with its DOM element. This is\n// used by the IntersectionObserver to track the link's visibility.\nconst prefetchable = typeof WeakMap === 'function' ? new WeakMap() : new Map();\n// A Set of the currently visible links. We re-prefetch visible links after a\n// cache invalidation, or when the current URL changes. It's a separate data\n// structure from the WeakMap above because only the visible links need to\n// be enumerated.\nconst prefetchableAndVisible = new Set();\n// A single IntersectionObserver instance shared by all <Link> components.\nconst observer = typeof IntersectionObserver === 'function' ? new IntersectionObserver(handleIntersect, {\n    rootMargin: '200px'\n}) : null;\nfunction observeVisibility(element, instance) {\n    const existingInstance = prefetchable.get(element);\n    if (existingInstance !== undefined) {\n        // This shouldn't happen because each <Link> component should have its own\n        // anchor tag instance, but it's defensive coding to avoid a memory leak in\n        // case there's a logical error somewhere else.\n        unmountPrefetchableInstance(element);\n    }\n    // Only track prefetchable links that have a valid prefetch URL\n    prefetchable.set(element, instance);\n    if (observer !== null) {\n        observer.observe(element);\n    }\n}\nfunction coercePrefetchableUrl(href) {\n    try {\n        return (0, _approuter.createPrefetchURL)(href);\n    } catch (e) {\n        // createPrefetchURL sometimes throws an error if an invalid URL is\n        // provided, though I'm not sure if it's actually necessary.\n        // TODO: Consider removing the throw from the inner function, or change it\n        // to reportError. Or maybe the error isn't even necessary for automatic\n        // prefetches, just navigations.\n        const reportErrorFn = typeof reportError === 'function' ? reportError : console.error;\n        reportErrorFn(\"Cannot prefetch '\" + href + \"' because it cannot be converted to a URL.\");\n        return null;\n    }\n}\nfunction mountLinkInstance(element, href, router, kind, prefetchEnabled, setOptimisticLinkStatus) {\n    if (prefetchEnabled) {\n        const prefetchURL = coercePrefetchableUrl(href);\n        if (prefetchURL !== null) {\n            const instance = {\n                router,\n                kind,\n                isVisible: false,\n                wasHoveredOrTouched: false,\n                prefetchTask: null,\n                cacheVersion: -1,\n                prefetchHref: prefetchURL.href,\n                setOptimisticLinkStatus\n            };\n            // We only observe the link's visibility if it's prefetchable. For\n            // example, this excludes links to external URLs.\n            observeVisibility(element, instance);\n            return instance;\n        }\n    }\n    // If the link is not prefetchable, we still create an instance so we can\n    // track its optimistic state (i.e. useLinkStatus).\n    const instance = {\n        router,\n        kind,\n        isVisible: false,\n        wasHoveredOrTouched: false,\n        prefetchTask: null,\n        cacheVersion: -1,\n        prefetchHref: null,\n        setOptimisticLinkStatus\n    };\n    return instance;\n}\nfunction mountFormInstance(element, href, router, kind) {\n    const prefetchURL = coercePrefetchableUrl(href);\n    if (prefetchURL === null) {\n        // This href is not prefetchable, so we don't track it.\n        // TODO: We currently observe/unobserve a form every time its href changes.\n        // For Links, this isn't a big deal because the href doesn't usually change,\n        // but for forms it's extremely common. We should optimize this.\n        return;\n    }\n    const instance = {\n        router,\n        kind,\n        isVisible: false,\n        wasHoveredOrTouched: false,\n        prefetchTask: null,\n        cacheVersion: -1,\n        prefetchHref: prefetchURL.href,\n        setOptimisticLinkStatus: null\n    };\n    observeVisibility(element, instance);\n}\nfunction unmountPrefetchableInstance(element) {\n    const instance = prefetchable.get(element);\n    if (instance !== undefined) {\n        prefetchable.delete(element);\n        prefetchableAndVisible.delete(instance);\n        const prefetchTask = instance.prefetchTask;\n        if (prefetchTask !== null) {\n            (0, _segmentcache.cancelPrefetchTask)(prefetchTask);\n        }\n    }\n    if (observer !== null) {\n        observer.unobserve(element);\n    }\n}\nfunction handleIntersect(entries) {\n    for (const entry of entries){\n        // Some extremely old browsers or polyfills don't reliably support\n        // isIntersecting so we check intersectionRatio instead. (Do we care? Not\n        // really. But whatever this is fine.)\n        const isVisible = entry.intersectionRatio > 0;\n        onLinkVisibilityChanged(entry.target, isVisible);\n    }\n}\nfunction onLinkVisibilityChanged(element, isVisible) {\n    if (true) {\n        // Prefetching on viewport is disabled in development for performance\n        // reasons, because it requires compiling the target page.\n        // TODO: Investigate re-enabling this.\n        return;\n    }\n    const instance = prefetchable.get(element);\n    if (instance === undefined) {\n        return;\n    }\n    instance.isVisible = isVisible;\n    if (isVisible) {\n        prefetchableAndVisible.add(instance);\n    } else {\n        prefetchableAndVisible.delete(instance);\n    }\n    rescheduleLinkPrefetch(instance);\n}\nfunction onNavigationIntent(element, unstable_upgradeToDynamicPrefetch) {\n    const instance = prefetchable.get(element);\n    if (instance === undefined) {\n        return;\n    }\n    // Prefetch the link on hover/touchstart.\n    if (instance !== undefined) {\n        instance.wasHoveredOrTouched = true;\n        if (false) {}\n        rescheduleLinkPrefetch(instance);\n    }\n}\nfunction rescheduleLinkPrefetch(instance) {\n    const existingPrefetchTask = instance.prefetchTask;\n    if (!instance.isVisible) {\n        // Cancel any in-progress prefetch task. (If it already finished then this\n        // is a no-op.)\n        if (existingPrefetchTask !== null) {\n            (0, _segmentcache.cancelPrefetchTask)(existingPrefetchTask);\n        }\n        // We don't need to reset the prefetchTask to null upon cancellation; an\n        // old task object can be rescheduled with reschedulePrefetchTask. This is a\n        // micro-optimization but also makes the code simpler (don't need to\n        // worry about whether an old task object is stale).\n        return;\n    }\n    if (true) {\n        // The old prefetch implementation does not have different priority levels.\n        // Just schedule a new prefetch task.\n        prefetchWithOldCacheImplementation(instance);\n        return;\n    }\n    // In the Segment Cache implementation, we assign a higher priority level to\n    // links that were at one point hovered or touched. Since the queue is last-\n    // in-first-out, the highest priority Link is whichever one was hovered last.\n    //\n    // We also increase the relative priority of links whenever they re-enter the\n    // viewport, as if they were being scheduled for the first time.\n    const priority = instance.wasHoveredOrTouched ? _segmentcache.PrefetchPriority.Intent : _segmentcache.PrefetchPriority.Default;\n    const appRouterState = (0, _approuterinstance.getCurrentAppRouterState)();\n    if (appRouterState !== null) {\n        const treeAtTimeOfPrefetch = appRouterState.tree;\n        if (existingPrefetchTask === null) {\n            // Initiate a prefetch task.\n            const nextUrl = appRouterState.nextUrl;\n            const cacheKey = (0, _segmentcache.createCacheKey)(instance.prefetchHref, nextUrl);\n            instance.prefetchTask = (0, _segmentcache.schedulePrefetchTask)(cacheKey, treeAtTimeOfPrefetch, instance.kind === _routerreducertypes.PrefetchKind.FULL, priority);\n        } else {\n            // We already have an old task object that we can reschedule. This is\n            // effectively the same as canceling the old task and creating a new one.\n            (0, _segmentcache.reschedulePrefetchTask)(existingPrefetchTask, treeAtTimeOfPrefetch, instance.kind === _routerreducertypes.PrefetchKind.FULL, priority);\n        }\n        // Keep track of the cache version at the time the prefetch was requested.\n        // This is used to check if the prefetch is stale.\n        instance.cacheVersion = (0, _segmentcache.getCurrentCacheVersion)();\n    }\n}\nfunction pingVisibleLinks(nextUrl, tree) {\n    // For each currently visible link, cancel the existing prefetch task (if it\n    // exists) and schedule a new one. This is effectively the same as if all the\n    // visible links left and then re-entered the viewport.\n    //\n    // This is called when the Next-Url or the base tree changes, since those\n    // may affect the result of a prefetch task. It's also called after a\n    // cache invalidation.\n    const currentCacheVersion = (0, _segmentcache.getCurrentCacheVersion)();\n    for (const instance of prefetchableAndVisible){\n        const task = instance.prefetchTask;\n        if (task !== null && instance.cacheVersion === currentCacheVersion && task.key.nextUrl === nextUrl && task.treeAtTimeOfPrefetch === tree) {\n            continue;\n        }\n        // Something changed. Cancel the existing prefetch task and schedule a\n        // new one.\n        if (task !== null) {\n            (0, _segmentcache.cancelPrefetchTask)(task);\n        }\n        const cacheKey = (0, _segmentcache.createCacheKey)(instance.prefetchHref, nextUrl);\n        const priority = instance.wasHoveredOrTouched ? _segmentcache.PrefetchPriority.Intent : _segmentcache.PrefetchPriority.Default;\n        instance.prefetchTask = (0, _segmentcache.schedulePrefetchTask)(cacheKey, tree, instance.kind === _routerreducertypes.PrefetchKind.FULL, priority);\n        instance.cacheVersion = (0, _segmentcache.getCurrentCacheVersion)();\n    }\n}\nfunction prefetchWithOldCacheImplementation(instance) {\n    // This is the path used when the Segment Cache is not enabled.\n    if (false) {}\n    const doPrefetch = async ()=>{\n        // note that `appRouter.prefetch()` is currently sync,\n        // so we have to wrap this call in an async function to be able to catch() errors below.\n        return instance.router.prefetch(instance.prefetchHref, {\n            kind: instance.kind\n        });\n    };\n    // Prefetch the page if asked (only in the client)\n    // We need to handle a prefetch error here since we may be\n    // loading with priority which can reject but we don't\n    // want to force navigation since this is only a prefetch\n    doPrefetch().catch((err)=>{\n        if (true) {\n            // rethrow to show invalid URL errors\n            throw err;\n        }\n    });\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=links.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/links.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay.js ***!
  \*******************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"AppDevOverlay\", ({\n    enumerable: true,\n    get: function() {\n        return AppDevOverlay;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _appdevoverlayerrorboundary = __webpack_require__(/*! ./app-dev-overlay-error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay-error-boundary.js\");\nconst _fontstyles = __webpack_require__(/*! ../font/font-styles */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/font/font-styles.js\");\nconst _devoverlay = __webpack_require__(/*! ../ui/dev-overlay */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/dev-overlay.js\");\nconst _useerrorhandler = __webpack_require__(/*! ../../errors/use-error-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/use-error-handler.js\");\nconst _isnextroutererror = __webpack_require__(/*! ../../is-next-router-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/is-next-router-error.js\");\nconst _constants = __webpack_require__(/*! ../../../../shared/lib/errors/constants */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/errors/constants.js\");\nfunction readSsrError() {\n    if (typeof document === 'undefined') {\n        return null;\n    }\n    const ssrErrorTemplateTag = document.querySelector('template[data-next-error-message]');\n    if (ssrErrorTemplateTag) {\n        const message = ssrErrorTemplateTag.getAttribute('data-next-error-message');\n        const stack = ssrErrorTemplateTag.getAttribute('data-next-error-stack');\n        const digest = ssrErrorTemplateTag.getAttribute('data-next-error-digest');\n        const error = Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n            value: \"E394\",\n            enumerable: false,\n            configurable: true\n        });\n        if (digest) {\n            ;\n            error.digest = digest;\n        }\n        // Skip Next.js SSR'd internal errors that which will be handled by the error boundaries.\n        if ((0, _isnextroutererror.isNextRouterError)(error)) {\n            return null;\n        }\n        error.stack = stack || '';\n        return error;\n    }\n    return null;\n}\n// Needs to be in the same error boundary as the shell.\n// If it commits, we know we recovered from an SSR error.\n// If it doesn't commit, we errored again and React will take care of error reporting.\nfunction ReplaySsrOnlyErrors(param) {\n    let { onBlockingError } = param;\n    if (true) {\n        // Need to read during render. The attributes will be gone after commit.\n        const ssrError = readSsrError();\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        (0, _react.useEffect)(()=>{\n            if (ssrError !== null) {\n                // TODO(veil): Produces wrong Owner Stack\n                // TODO(veil): Mark as recoverable error\n                // TODO(veil): console.error\n                (0, _useerrorhandler.handleClientError)(ssrError);\n                // If it's missing root tags, we can't recover, make it blocking.\n                if (ssrError.digest === _constants.MISSING_ROOT_TAGS_ERROR) {\n                    onBlockingError();\n                }\n            }\n        }, [\n            ssrError,\n            onBlockingError\n        ]);\n    }\n    return null;\n}\n_c = ReplaySsrOnlyErrors;\nfunction AppDevOverlay(param) {\n    let { state, globalError, children } = param;\n    const [isErrorOverlayOpen, setIsErrorOverlayOpen] = (0, _react.useState)(false);\n    const openOverlay = (0, _react.useCallback)(()=>{\n        setIsErrorOverlayOpen(true);\n    }, []);\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(_appdevoverlayerrorboundary.AppDevOverlayErrorBoundary, {\n                globalError: globalError,\n                onError: setIsErrorOverlayOpen,\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(ReplaySsrOnlyErrors, {\n                        onBlockingError: openOverlay\n                    }),\n                    children\n                ]\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_fontstyles.FontStyles, {}),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_devoverlay.DevOverlay, {\n                        state: state,\n                        isErrorOverlayOpen: isErrorOverlayOpen,\n                        setIsErrorOverlayOpen: setIsErrorOverlayOpen\n                    })\n                ]\n            })\n        ]\n    });\n}\n_c1 = AppDevOverlay;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-dev-overlay.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"ReplaySsrOnlyErrors\");\n$RefreshReg$(_c1, \"AppDevOverlay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js ***!
  \************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"createInitialRouterState\", ({\n    enumerable: true,\n    get: function() {\n        return createInitialRouterState;\n    }\n}));\nconst _createhreffromurl = __webpack_require__(/*! ./create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _filllazyitemstillleafwithhead = __webpack_require__(/*! ./fill-lazy-items-till-leaf-with-head */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fill-lazy-items-till-leaf-with-head.js\");\nconst _computechangedpath = __webpack_require__(/*! ./compute-changed-path */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/compute-changed-path.js\");\nconst _prefetchcacheutils = __webpack_require__(/*! ./prefetch-cache-utils */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _refetchinactiveparallelsegments = __webpack_require__(/*! ./refetch-inactive-parallel-segments */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/refetch-inactive-parallel-segments.js\");\nconst _flightdatahelpers = __webpack_require__(/*! ../../flight-data-helpers */ \"(app-pages-browser)/./node_modules/next/dist/client/flight-data-helpers.js\");\nfunction createInitialRouterState(param) {\n    let { navigatedAt, initialFlightData, initialCanonicalUrlParts, initialParallelRoutes, location, couldBeIntercepted, postponed, prerendered } = param;\n    // When initialized on the server, the canonical URL is provided as an array of parts.\n    // This is to ensure that when the RSC payload streamed to the client, crawlers don't interpret it\n    // as a URL that should be crawled.\n    const initialCanonicalUrl = initialCanonicalUrlParts.join('/');\n    const normalizedFlightData = (0, _flightdatahelpers.getFlightDataPartsFromPath)(initialFlightData[0]);\n    const { tree: initialTree, seedData: initialSeedData, head: initialHead } = normalizedFlightData;\n    // For the SSR render, seed data should always be available (we only send back a `null` response\n    // in the case of a `loading` segment, pre-PPR.)\n    const rsc = initialSeedData == null ? void 0 : initialSeedData[1];\n    var _initialSeedData_;\n    const loading = (_initialSeedData_ = initialSeedData == null ? void 0 : initialSeedData[3]) != null ? _initialSeedData_ : null;\n    const cache = {\n        lazyData: null,\n        rsc,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        // The cache gets seeded during the first render. `initialParallelRoutes` ensures the cache from the first render is there during the second render.\n        parallelRoutes: initialParallelRoutes,\n        loading,\n        navigatedAt\n    };\n    const canonicalUrl = // This is safe to do as canonicalUrl can't be rendered, it's only used to control the history updates in the useEffect further down in this file.\n    location ? (0, _createhreffromurl.createHrefFromUrl)(location) : initialCanonicalUrl;\n    (0, _refetchinactiveparallelsegments.addRefreshMarkerToActiveParallelSegments)(initialTree, canonicalUrl);\n    const prefetchCache = new Map();\n    // When the cache hasn't been seeded yet we fill the cache with the head.\n    if (initialParallelRoutes === null || initialParallelRoutes.size === 0) {\n        (0, _filllazyitemstillleafwithhead.fillLazyItemsTillLeafWithHead)(navigatedAt, cache, undefined, initialTree, initialSeedData, initialHead, undefined);\n    }\n    var _ref;\n    const initialState = {\n        tree: initialTree,\n        cache,\n        prefetchCache,\n        pushRef: {\n            pendingPush: false,\n            mpaNavigation: false,\n            // First render needs to preserve the previous window.history.state\n            // to avoid it being overwritten on navigation back/forward with MPA Navigation.\n            preserveCustomHistoryState: true\n        },\n        focusAndScrollRef: {\n            apply: false,\n            onlyHashChange: false,\n            hashFragment: null,\n            segmentPaths: []\n        },\n        canonicalUrl,\n        nextUrl: (_ref = (0, _computechangedpath.extractPathFromFlightRouterState)(initialTree) || (location == null ? void 0 : location.pathname)) != null ? _ref : null\n    };\n    if (false) {}\n    return initialState;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=create-initial-router-state.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js ***!
  \******************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createFetch: function() {\n        return createFetch;\n    },\n    createFromNextReadableStream: function() {\n        return createFromNextReadableStream;\n    },\n    fetchServerResponse: function() {\n        return fetchServerResponse;\n    },\n    urlToUrlWithoutFlightMarker: function() {\n        return urlToUrlWithoutFlightMarker;\n    }\n});\nconst _approuterheaders = __webpack_require__(/*! ../app-router-headers */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-headers.js\");\nconst _appcallserver = __webpack_require__(/*! ../../app-call-server */ \"(app-pages-browser)/./node_modules/next/dist/client/app-call-server.js\");\nconst _appfindsourcemapurl = __webpack_require__(/*! ../../app-find-source-map-url */ \"(app-pages-browser)/./node_modules/next/dist/client/app-find-source-map-url.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _flightdatahelpers = __webpack_require__(/*! ../../flight-data-helpers */ \"(app-pages-browser)/./node_modules/next/dist/client/flight-data-helpers.js\");\nconst _appbuildid = __webpack_require__(/*! ../../app-build-id */ \"(app-pages-browser)/./node_modules/next/dist/client/app-build-id.js\");\nconst _setcachebustingsearchparam = __webpack_require__(/*! ./set-cache-busting-search-param */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/set-cache-busting-search-param.js\");\n// @ts-ignore\n// eslint-disable-next-line import/no-extraneous-dependencies\n// import { createFromReadableStream } from 'react-server-dom-webpack/client'\nconst { createFromReadableStream } =  false ? 0 : __webpack_require__(/*! react-server-dom-webpack/client */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/client.js\");\nfunction urlToUrlWithoutFlightMarker(url) {\n    const urlWithoutFlightParameters = new URL(url, location.origin);\n    urlWithoutFlightParameters.searchParams.delete(_approuterheaders.NEXT_RSC_UNION_QUERY);\n    if (false) {}\n    return urlWithoutFlightParameters;\n}\nfunction doMpaNavigation(url) {\n    return {\n        flightData: urlToUrlWithoutFlightMarker(url).toString(),\n        canonicalUrl: undefined,\n        couldBeIntercepted: false,\n        prerendered: false,\n        postponed: false,\n        staleTime: -1\n    };\n}\nlet abortController = new AbortController();\nif (true) {\n    // Abort any in-flight requests when the page is unloaded, e.g. due to\n    // reloading the page or performing hard navigations. This allows us to ignore\n    // what would otherwise be a thrown TypeError when the browser cancels the\n    // requests.\n    window.addEventListener('pagehide', ()=>{\n        abortController.abort();\n    });\n    // Use a fresh AbortController instance on pageshow, e.g. when navigating back\n    // and the JavaScript execution context is restored by the browser.\n    window.addEventListener('pageshow', ()=>{\n        abortController = new AbortController();\n    });\n}\nasync function fetchServerResponse(url, options) {\n    const { flightRouterState, nextUrl, prefetchKind } = options;\n    const headers = {\n        // Enable flight response\n        [_approuterheaders.RSC_HEADER]: '1',\n        // Provide the current router state\n        [_approuterheaders.NEXT_ROUTER_STATE_TREE_HEADER]: encodeURIComponent(JSON.stringify(flightRouterState))\n    };\n    /**\n   * Three cases:\n   * - `prefetchKind` is `undefined`, it means it's a normal navigation, so we want to prefetch the page data fully\n   * - `prefetchKind` is `full` - we want to prefetch the whole page so same as above\n   * - `prefetchKind` is `auto` - if the page is dynamic, prefetch the page data partially, if static prefetch the page data fully\n   */ if (prefetchKind === _routerreducertypes.PrefetchKind.AUTO) {\n        headers[_approuterheaders.NEXT_ROUTER_PREFETCH_HEADER] = '1';\n    }\n    if ( true && options.isHmrRefresh) {\n        headers[_approuterheaders.NEXT_HMR_REFRESH_HEADER] = '1';\n    }\n    if (nextUrl) {\n        headers[_approuterheaders.NEXT_URL] = nextUrl;\n    }\n    try {\n        var _res_headers_get;\n        // When creating a \"temporary\" prefetch (the \"on-demand\" prefetch that gets created on navigation, if one doesn't exist)\n        // we send the request with a \"high\" priority as it's in response to a user interaction that could be blocking a transition.\n        // Otherwise, all other prefetches are sent with a \"low\" priority.\n        // We use \"auto\" for in all other cases to match the existing default, as this function is shared outside of prefetching.\n        const fetchPriority = prefetchKind ? prefetchKind === _routerreducertypes.PrefetchKind.TEMPORARY ? 'high' : 'low' : 'auto';\n        if (false) {}\n        const res = await createFetch(url, headers, fetchPriority, abortController.signal);\n        const responseUrl = urlToUrlWithoutFlightMarker(res.url);\n        const canonicalUrl = res.redirected ? responseUrl : undefined;\n        const contentType = res.headers.get('content-type') || '';\n        const interception = !!((_res_headers_get = res.headers.get('vary')) == null ? void 0 : _res_headers_get.includes(_approuterheaders.NEXT_URL));\n        const postponed = !!res.headers.get(_approuterheaders.NEXT_DID_POSTPONE_HEADER);\n        const staleTimeHeaderSeconds = res.headers.get(_approuterheaders.NEXT_ROUTER_STALE_TIME_HEADER);\n        const staleTime = staleTimeHeaderSeconds !== null ? parseInt(staleTimeHeaderSeconds, 10) * 1000 : -1;\n        let isFlightResponse = contentType.startsWith(_approuterheaders.RSC_CONTENT_TYPE_HEADER);\n        if (false) {}\n        // If fetch returns something different than flight response handle it like a mpa navigation\n        // If the fetch was not 200, we also handle it like a mpa navigation\n        if (!isFlightResponse || !res.ok || !res.body) {\n            // in case the original URL came with a hash, preserve it before redirecting to the new URL\n            if (url.hash) {\n                responseUrl.hash = url.hash;\n            }\n            return doMpaNavigation(responseUrl.toString());\n        }\n        // We may navigate to a page that requires a different Webpack runtime.\n        // In prod, every page will have the same Webpack runtime.\n        // In dev, the Webpack runtime is minimal for each page.\n        // We need to ensure the Webpack runtime is updated before executing client-side JS of the new page.\n        if (true) {\n            await (__webpack_require__(/*! ../react-dev-overlay/app/hot-reloader-client */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js\").waitForWebpackRuntimeHotUpdate)();\n        }\n        // Handle the `fetch` readable stream that can be unwrapped by `React.use`.\n        const flightStream = postponed ? createUnclosingPrefetchStream(res.body) : res.body;\n        const response = await createFromNextReadableStream(flightStream);\n        if ((0, _appbuildid.getAppBuildId)() !== response.b) {\n            return doMpaNavigation(res.url);\n        }\n        return {\n            flightData: (0, _flightdatahelpers.normalizeFlightData)(response.f),\n            canonicalUrl: canonicalUrl,\n            couldBeIntercepted: interception,\n            prerendered: response.S,\n            postponed,\n            staleTime\n        };\n    } catch (err) {\n        if (!abortController.signal.aborted) {\n            console.error(\"Failed to fetch RSC payload for \" + url + \". Falling back to browser navigation.\", err);\n        }\n        // If fetch fails handle it like a mpa navigation\n        // TODO-APP: Add a test for the case where a CORS request fails, e.g. external url redirect coming from the response.\n        // See https://github.com/vercel/next.js/issues/43605#issuecomment-1451617521 for a reproduction.\n        return {\n            flightData: url.toString(),\n            canonicalUrl: undefined,\n            couldBeIntercepted: false,\n            prerendered: false,\n            postponed: false,\n            staleTime: -1\n        };\n    }\n}\nfunction createFetch(url, headers, fetchPriority, signal) {\n    const fetchUrl = new URL(url);\n    // TODO: In output: \"export\" mode, the headers do nothing. Omit them (and the\n    // cache busting search param) from the request so they're\n    // maximally cacheable.\n    (0, _setcachebustingsearchparam.setCacheBustingSearchParam)(fetchUrl, headers);\n    if (false) {}\n    if (false) {}\n    return fetch(fetchUrl, {\n        // Backwards compat for older browsers. `same-origin` is the default in modern browsers.\n        credentials: 'same-origin',\n        headers,\n        priority: fetchPriority || undefined,\n        signal\n    });\n}\nfunction createFromNextReadableStream(flightStream) {\n    return createFromReadableStream(flightStream, {\n        callServer: _appcallserver.callServer,\n        findSourceMapURL: _appfindsourcemapurl.findSourceMapURL\n    });\n}\nfunction createUnclosingPrefetchStream(originalFlightStream) {\n    // When PPR is enabled, prefetch streams may contain references that never\n    // resolve, because that's how we encode dynamic data access. In the decoded\n    // object returned by the Flight client, these are reified into hanging\n    // promises that suspend during render, which is effectively what we want.\n    // The UI resolves when it switches to the dynamic data stream\n    // (via useDeferredValue(dynamic, static)).\n    //\n    // However, the Flight implementation currently errors if the server closes\n    // the response before all the references are resolved. As a cheat to work\n    // around this, we wrap the original stream in a new stream that never closes,\n    // and therefore doesn't error.\n    const reader = originalFlightStream.getReader();\n    return new ReadableStream({\n        async pull (controller) {\n            while(true){\n                const { done, value } = await reader.read();\n                if (!done) {\n                    // Pass to the target stream and keep consuming the Flight response\n                    // from the server.\n                    controller.enqueue(value);\n                    continue;\n                }\n                // The server stream has closed. Exit, but intentionally do not close\n                // the target stream.\n                return;\n            }\n        }\n    });\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=fetch-server-response.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js ***!
  \*****************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DYNAMIC_STALETIME_MS: function() {\n        return DYNAMIC_STALETIME_MS;\n    },\n    STATIC_STALETIME_MS: function() {\n        return STATIC_STALETIME_MS;\n    },\n    createSeededPrefetchCacheEntry: function() {\n        return createSeededPrefetchCacheEntry;\n    },\n    getOrCreatePrefetchCacheEntry: function() {\n        return getOrCreatePrefetchCacheEntry;\n    },\n    prunePrefetchCache: function() {\n        return prunePrefetchCache;\n    }\n});\nconst _fetchserverresponse = __webpack_require__(/*! ./fetch-server-response */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _prefetchreducer = __webpack_require__(/*! ./reducers/prefetch-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/prefetch-reducer.js\");\nconst INTERCEPTION_CACHE_KEY_MARKER = '%';\n/**\n * Creates a cache key for the router prefetch cache\n *\n * @param url - The URL being navigated to\n * @param nextUrl - an internal URL, primarily used for handling rewrites. Defaults to '/'.\n * @return The generated prefetch cache key.\n */ function createPrefetchCacheKeyImpl(url, includeSearchParams, prefix) {\n    // Initially we only use the pathname as the cache key. We don't want to include\n    // search params so that multiple URLs with the same search parameter can re-use\n    // loading states.\n    let pathnameFromUrl = url.pathname;\n    // RSC responses can differ based on search params, specifically in the case where we aren't\n    // returning a partial response (ie with `PrefetchKind.AUTO`).\n    // In the auto case, since loading.js & layout.js won't have access to search params,\n    // we can safely re-use that cache entry. But for full prefetches, we should not\n    // re-use the cache entry as the response may differ.\n    if (includeSearchParams) {\n        // if we have a full prefetch, we can include the search param in the key,\n        // as we'll be getting back a full response. The server might have read the search\n        // params when generating the full response.\n        pathnameFromUrl += url.search;\n    }\n    if (prefix) {\n        return \"\" + prefix + INTERCEPTION_CACHE_KEY_MARKER + pathnameFromUrl;\n    }\n    return pathnameFromUrl;\n}\nfunction createPrefetchCacheKey(url, kind, nextUrl) {\n    return createPrefetchCacheKeyImpl(url, kind === _routerreducertypes.PrefetchKind.FULL, nextUrl);\n}\nfunction getExistingCacheEntry(url, kind, nextUrl, prefetchCache, allowAliasing) {\n    if (kind === void 0) kind = _routerreducertypes.PrefetchKind.TEMPORARY;\n    // We first check if there's a more specific interception route prefetch entry\n    // This is because when we detect a prefetch that corresponds with an interception route, we prefix it with nextUrl (see `createPrefetchCacheKey`)\n    // to avoid conflicts with other pages that may have the same URL but render different things depending on the `Next-URL` header.\n    for (const maybeNextUrl of [\n        nextUrl,\n        null\n    ]){\n        const cacheKeyWithParams = createPrefetchCacheKeyImpl(url, true, maybeNextUrl);\n        const cacheKeyWithoutParams = createPrefetchCacheKeyImpl(url, false, maybeNextUrl);\n        // First, we check if we have a cache entry that exactly matches the URL\n        const cacheKeyToUse = url.search ? cacheKeyWithParams : cacheKeyWithoutParams;\n        const existingEntry = prefetchCache.get(cacheKeyToUse);\n        if (existingEntry && allowAliasing) {\n            // We know we're returning an aliased entry when the pathname matches but the search params don't,\n            const isAliased = existingEntry.url.pathname === url.pathname && existingEntry.url.search !== url.search;\n            if (isAliased) {\n                return {\n                    ...existingEntry,\n                    aliased: true\n                };\n            }\n            return existingEntry;\n        }\n        // If the request contains search params, and we're not doing a full prefetch, we can return the\n        // param-less entry if it exists.\n        // This is technically covered by the check at the bottom of this function, which iterates over cache entries,\n        // but lets us arrive there quicker in the param-full case.\n        const entryWithoutParams = prefetchCache.get(cacheKeyWithoutParams);\n        if (false) {}\n    }\n    // If we've gotten to this point, we didn't find a specific cache entry that matched\n    // the request URL.\n    // We attempt a partial match by checking if there's a cache entry with the same pathname.\n    // Regardless of what we find, since it doesn't correspond with the requested URL, we'll mark it \"aliased\".\n    // This will signal to the router that it should only apply the loading state on the prefetched data.\n    if (false) {}\n    return undefined;\n}\nfunction getOrCreatePrefetchCacheEntry(param) {\n    let { url, nextUrl, tree, prefetchCache, kind, allowAliasing = true } = param;\n    const existingCacheEntry = getExistingCacheEntry(url, kind, nextUrl, prefetchCache, allowAliasing);\n    if (existingCacheEntry) {\n        // Grab the latest status of the cache entry and update it\n        existingCacheEntry.status = getPrefetchEntryCacheStatus(existingCacheEntry);\n        // when `kind` is provided, an explicit prefetch was requested.\n        // if the requested prefetch is \"full\" and the current cache entry wasn't, we want to re-prefetch with the new intent\n        const switchedToFullPrefetch = existingCacheEntry.kind !== _routerreducertypes.PrefetchKind.FULL && kind === _routerreducertypes.PrefetchKind.FULL;\n        if (switchedToFullPrefetch) {\n            // If we switched to a full prefetch, validate that the existing cache entry contained partial data.\n            // It's possible that the cache entry was seeded with full data but has a cache type of \"auto\" (ie when cache entries\n            // are seeded but without a prefetch intent)\n            existingCacheEntry.data.then((prefetchResponse)=>{\n                const isFullPrefetch = Array.isArray(prefetchResponse.flightData) && prefetchResponse.flightData.some((flightData)=>{\n                    // If we started rendering from the root and we returned RSC data (seedData), we already had a full prefetch.\n                    return flightData.isRootRender && flightData.seedData !== null;\n                });\n                if (!isFullPrefetch) {\n                    return createLazyPrefetchEntry({\n                        tree,\n                        url,\n                        nextUrl,\n                        prefetchCache,\n                        // If we didn't get an explicit prefetch kind, we want to set a temporary kind\n                        // rather than assuming the same intent as the previous entry, to be consistent with how we\n                        // lazily create prefetch entries when intent is left unspecified.\n                        kind: kind != null ? kind : _routerreducertypes.PrefetchKind.TEMPORARY\n                    });\n                }\n            });\n        }\n        // If the existing cache entry was marked as temporary, it means it was lazily created when attempting to get an entry,\n        // where we didn't have the prefetch intent. Now that we have the intent (in `kind`), we want to update the entry to the more accurate kind.\n        if (kind && existingCacheEntry.kind === _routerreducertypes.PrefetchKind.TEMPORARY) {\n            existingCacheEntry.kind = kind;\n        }\n        // We've determined that the existing entry we found is still valid, so we return it.\n        return existingCacheEntry;\n    }\n    // If we didn't return an entry, create a new one.\n    return createLazyPrefetchEntry({\n        tree,\n        url,\n        nextUrl,\n        prefetchCache,\n        kind: kind || _routerreducertypes.PrefetchKind.TEMPORARY\n    });\n}\n/*\n * Used to take an existing cache entry and prefix it with the nextUrl, if it exists.\n * This ensures that we don't have conflicting cache entries for the same URL (as is the case with route interception).\n */ function prefixExistingPrefetchCacheEntry(param) {\n    let { url, nextUrl, prefetchCache, existingCacheKey } = param;\n    const existingCacheEntry = prefetchCache.get(existingCacheKey);\n    if (!existingCacheEntry) {\n        // no-op -- there wasn't an entry to move\n        return;\n    }\n    const newCacheKey = createPrefetchCacheKey(url, existingCacheEntry.kind, nextUrl);\n    prefetchCache.set(newCacheKey, {\n        ...existingCacheEntry,\n        key: newCacheKey\n    });\n    prefetchCache.delete(existingCacheKey);\n    return newCacheKey;\n}\nfunction createSeededPrefetchCacheEntry(param) {\n    let { nextUrl, tree, prefetchCache, url, data, kind } = param;\n    // The initial cache entry technically includes full data, but it isn't explicitly prefetched -- we just seed the\n    // prefetch cache so that we can skip an extra prefetch request later, since we already have the data.\n    // if the prefetch corresponds with an interception route, we use the nextUrl to prefix the cache key\n    const prefetchCacheKey = data.couldBeIntercepted ? createPrefetchCacheKey(url, kind, nextUrl) : createPrefetchCacheKey(url, kind);\n    const prefetchEntry = {\n        treeAtTimeOfPrefetch: tree,\n        data: Promise.resolve(data),\n        kind,\n        prefetchTime: Date.now(),\n        lastUsedTime: Date.now(),\n        staleTime: -1,\n        key: prefetchCacheKey,\n        status: _routerreducertypes.PrefetchCacheEntryStatus.fresh,\n        url\n    };\n    prefetchCache.set(prefetchCacheKey, prefetchEntry);\n    return prefetchEntry;\n}\n/**\n * Creates a prefetch entry entry and enqueues a fetch request to retrieve the data.\n */ function createLazyPrefetchEntry(param) {\n    let { url, kind, tree, nextUrl, prefetchCache } = param;\n    const prefetchCacheKey = createPrefetchCacheKey(url, kind);\n    // initiates the fetch request for the prefetch and attaches a listener\n    // to the promise to update the prefetch cache entry when the promise resolves (if necessary)\n    const data = _prefetchreducer.prefetchQueue.enqueue(()=>(0, _fetchserverresponse.fetchServerResponse)(url, {\n            flightRouterState: tree,\n            nextUrl,\n            prefetchKind: kind\n        }).then((prefetchResponse)=>{\n            // TODO: `fetchServerResponse` should be more tighly coupled to these prefetch cache operations\n            // to avoid drift between this cache key prefixing logic\n            // (which is currently directly influenced by the server response)\n            let newCacheKey;\n            if (prefetchResponse.couldBeIntercepted) {\n                // Determine if we need to prefix the cache key with the nextUrl\n                newCacheKey = prefixExistingPrefetchCacheEntry({\n                    url,\n                    existingCacheKey: prefetchCacheKey,\n                    nextUrl,\n                    prefetchCache\n                });\n            }\n            // If the prefetch was a cache hit, we want to update the existing cache entry to reflect that it was a full prefetch.\n            // This is because we know that a static response will contain the full RSC payload, and can be updated to respect the `static`\n            // staleTime.\n            if (prefetchResponse.prerendered) {\n                const existingCacheEntry = prefetchCache.get(newCacheKey != null ? newCacheKey : prefetchCacheKey);\n                if (existingCacheEntry) {\n                    existingCacheEntry.kind = _routerreducertypes.PrefetchKind.FULL;\n                    if (prefetchResponse.staleTime !== -1) {\n                        // This is the stale time that was collected by the server during\n                        // static generation. Use this in place of the default stale time.\n                        existingCacheEntry.staleTime = prefetchResponse.staleTime;\n                    }\n                }\n            }\n            return prefetchResponse;\n        }));\n    const prefetchEntry = {\n        treeAtTimeOfPrefetch: tree,\n        data,\n        kind,\n        prefetchTime: Date.now(),\n        lastUsedTime: null,\n        staleTime: -1,\n        key: prefetchCacheKey,\n        status: _routerreducertypes.PrefetchCacheEntryStatus.fresh,\n        url\n    };\n    prefetchCache.set(prefetchCacheKey, prefetchEntry);\n    return prefetchEntry;\n}\nfunction prunePrefetchCache(prefetchCache) {\n    for (const [href, prefetchCacheEntry] of prefetchCache){\n        if (getPrefetchEntryCacheStatus(prefetchCacheEntry) === _routerreducertypes.PrefetchCacheEntryStatus.expired) {\n            prefetchCache.delete(href);\n        }\n    }\n}\nconst DYNAMIC_STALETIME_MS = Number(\"0\") * 1000;\nconst STATIC_STALETIME_MS = Number(\"300\") * 1000;\nfunction getPrefetchEntryCacheStatus(param) {\n    let { kind, prefetchTime, lastUsedTime, staleTime } = param;\n    if (staleTime !== -1) {\n        // `staleTime` is the value sent by the server during static generation.\n        // When this is available, it takes precedence over any of the heuristics\n        // that follow.\n        //\n        // TODO: When PPR is enabled, the server will *always* return a stale time\n        // when prefetching. We should never use a prefetch entry that hasn't yet\n        // received data from the server. So the only two cases should be 1) we use\n        // the server-generated stale time 2) the unresolved entry is discarded.\n        return Date.now() < prefetchTime + staleTime ? _routerreducertypes.PrefetchCacheEntryStatus.fresh : _routerreducertypes.PrefetchCacheEntryStatus.stale;\n    }\n    // We will re-use the cache entry data for up to the `dynamic` staletime window.\n    if (Date.now() < (lastUsedTime != null ? lastUsedTime : prefetchTime) + DYNAMIC_STALETIME_MS) {\n        return lastUsedTime ? _routerreducertypes.PrefetchCacheEntryStatus.reusable : _routerreducertypes.PrefetchCacheEntryStatus.fresh;\n    }\n    // For \"auto\" prefetching, we'll re-use only the loading boundary for up to `static` staletime window.\n    // A stale entry will only re-use the `loading` boundary, not the full data.\n    // This will trigger a \"lazy fetch\" for the full data.\n    if (kind === _routerreducertypes.PrefetchKind.AUTO) {\n        if (Date.now() < prefetchTime + STATIC_STALETIME_MS) {\n            return _routerreducertypes.PrefetchCacheEntryStatus.stale;\n        }\n    }\n    // for \"full\" prefetching, we'll re-use the cache entry data for up to `static` staletime window.\n    if (kind === _routerreducertypes.PrefetchKind.FULL) {\n        if (Date.now() < prefetchTime + STATIC_STALETIME_MS) {\n            return _routerreducertypes.PrefetchCacheEntryStatus.reusable;\n        }\n    }\n    return _routerreducertypes.PrefetchCacheEntryStatus.expired;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=prefetch-cache-utils.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/hmr-refresh-reducer.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/reducers/hmr-refresh-reducer.js ***!
  \*************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"hmrRefreshReducer\", ({\n    enumerable: true,\n    get: function() {\n        return hmrRefreshReducer;\n    }\n}));\nconst _fetchserverresponse = __webpack_require__(/*! ../fetch-server-response */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\");\nconst _createhreffromurl = __webpack_require__(/*! ../create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _applyrouterstatepatchtotree = __webpack_require__(/*! ../apply-router-state-patch-to-tree */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/apply-router-state-patch-to-tree.js\");\nconst _isnavigatingtonewrootlayout = __webpack_require__(/*! ../is-navigating-to-new-root-layout */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/is-navigating-to-new-root-layout.js\");\nconst _navigatereducer = __webpack_require__(/*! ./navigate-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/navigate-reducer.js\");\nconst _handlemutable = __webpack_require__(/*! ../handle-mutable */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-mutable.js\");\nconst _applyflightdata = __webpack_require__(/*! ../apply-flight-data */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/apply-flight-data.js\");\nconst _approuter = __webpack_require__(/*! ../../app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\");\nconst _handlesegmentmismatch = __webpack_require__(/*! ../handle-segment-mismatch */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-segment-mismatch.js\");\nconst _hasinterceptionrouteincurrenttree = __webpack_require__(/*! ./has-interception-route-in-current-tree */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/has-interception-route-in-current-tree.js\");\n// A version of refresh reducer that keeps the cache around instead of wiping all of it.\nfunction hmrRefreshReducerImpl(state, action) {\n    const { origin } = action;\n    const mutable = {};\n    const href = state.canonicalUrl;\n    mutable.preserveCustomHistoryState = false;\n    const cache = (0, _approuter.createEmptyCacheNode)();\n    // If the current tree was intercepted, the nextUrl should be included in the request.\n    // This is to ensure that the refresh request doesn't get intercepted, accidentally triggering the interception route.\n    const includeNextUrl = (0, _hasinterceptionrouteincurrenttree.hasInterceptionRouteInCurrentTree)(state.tree);\n    // TODO-APP: verify that `href` is not an external url.\n    // Fetch data from the root of the tree.\n    const navigatedAt = Date.now();\n    cache.lazyData = (0, _fetchserverresponse.fetchServerResponse)(new URL(href, origin), {\n        flightRouterState: [\n            state.tree[0],\n            state.tree[1],\n            state.tree[2],\n            'refetch'\n        ],\n        nextUrl: includeNextUrl ? state.nextUrl : null,\n        isHmrRefresh: true\n    });\n    return cache.lazyData.then((param)=>{\n        let { flightData, canonicalUrl: canonicalUrlOverride } = param;\n        // Handle case when navigating to page in `pages` from `app`\n        if (typeof flightData === 'string') {\n            return (0, _navigatereducer.handleExternalUrl)(state, mutable, flightData, state.pushRef.pendingPush);\n        }\n        // Remove cache.lazyData as it has been resolved at this point.\n        cache.lazyData = null;\n        let currentTree = state.tree;\n        let currentCache = state.cache;\n        for (const normalizedFlightData of flightData){\n            const { tree: treePatch, isRootRender } = normalizedFlightData;\n            if (!isRootRender) {\n                // TODO-APP: handle this case better\n                console.log('REFRESH FAILED');\n                return state;\n            }\n            const newTree = (0, _applyrouterstatepatchtotree.applyRouterStatePatchToTree)([\n                ''\n            ], currentTree, treePatch, state.canonicalUrl);\n            if (newTree === null) {\n                return (0, _handlesegmentmismatch.handleSegmentMismatch)(state, action, treePatch);\n            }\n            if ((0, _isnavigatingtonewrootlayout.isNavigatingToNewRootLayout)(currentTree, newTree)) {\n                return (0, _navigatereducer.handleExternalUrl)(state, mutable, href, state.pushRef.pendingPush);\n            }\n            const canonicalUrlOverrideHref = canonicalUrlOverride ? (0, _createhreffromurl.createHrefFromUrl)(canonicalUrlOverride) : undefined;\n            if (canonicalUrlOverride) {\n                mutable.canonicalUrl = canonicalUrlOverrideHref;\n            }\n            const applied = (0, _applyflightdata.applyFlightData)(navigatedAt, currentCache, cache, normalizedFlightData);\n            if (applied) {\n                mutable.cache = cache;\n                currentCache = cache;\n            }\n            mutable.patchedTree = newTree;\n            mutable.canonicalUrl = href;\n            currentTree = newTree;\n        }\n        return (0, _handlemutable.handleMutable)(state, mutable);\n    }, ()=>state);\n}\nfunction hmrRefreshReducerNoop(state, _action) {\n    return state;\n}\nconst hmrRefreshReducer =  false ? 0 : hmrRefreshReducerImpl;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=hmr-refresh-reducer.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/hmr-refresh-reducer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/use-action-queue.js":
/*!**********************************************************************!*\
  !*** ./node_modules/next/dist/client/components/use-action-queue.js ***!
  \**********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    dispatchAppRouterAction: function() {\n        return dispatchAppRouterAction;\n    },\n    useActionQueue: function() {\n        return useActionQueue;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _isthenable = __webpack_require__(/*! ../../shared/lib/is-thenable */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/is-thenable.js\");\n// The app router state lives outside of React, so we can import the dispatch\n// method directly wherever we need it, rather than passing it around via props\n// or context.\nlet dispatch = null;\nfunction dispatchAppRouterAction(action) {\n    if (dispatch === null) {\n        throw Object.defineProperty(new Error('Internal Next.js error: Router action dispatched before initialization.'), \"__NEXT_ERROR_CODE\", {\n            value: \"E668\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    dispatch(action);\n}\nfunction useActionQueue(actionQueue) {\n    _s();\n    const [state, setState] = _react.default.useState(actionQueue.state);\n    // Because of a known issue that requires to decode Flight streams inside the\n    // render phase, we have to be a bit clever and assign the dispatch method to\n    // a module-level variable upon initialization. The useState hook in this\n    // module only exists to synchronize state that lives outside of React.\n    // Ideally, what we'd do instead is pass the state as a prop to root.render;\n    // this is conceptually how we're modeling the app router state, despite the\n    // weird implementation details.\n    if (true) {\n        const useSyncDevRenderIndicator = (__webpack_require__(/*! ./react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator.js\").useSyncDevRenderIndicator);\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const syncDevRenderIndicator = useSyncDevRenderIndicator();\n        dispatch = (action)=>{\n            syncDevRenderIndicator(()=>{\n                actionQueue.dispatch(action, setState);\n            });\n        };\n    } else {}\n    return (0, _isthenable.isThenable)(state) ? (0, _react.use)(state) : state;\n}\n_s(useActionQueue, \"Rp0Tj1zyE8LTecjN/cjTzn46xPo=\");\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-action-queue.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/use-action-queue.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/error-boundary-callbacks.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/next/dist/client/react-client-callbacks/error-boundary-callbacks.js ***!
  \******************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("// This file is only used in app router due to the specific error state handling.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    onCaughtError: function() {\n        return onCaughtError;\n    },\n    onUncaughtError: function() {\n        return onUncaughtError;\n    }\n});\nconst _stitchederror = __webpack_require__(/*! ../components/errors/stitched-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/stitched-error.js\");\nconst _useerrorhandler = __webpack_require__(/*! ../components/errors/use-error-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/use-error-handler.js\");\nconst _isnextroutererror = __webpack_require__(/*! ../components/is-next-router-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/is-next-router-error.js\");\nconst _bailouttocsr = __webpack_require__(/*! ../../shared/lib/lazy-dynamic/bailout-to-csr */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/bailout-to-csr.js\");\nconst _reportglobalerror = __webpack_require__(/*! ./report-global-error */ \"(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/report-global-error.js\");\nconst _interceptconsoleerror = __webpack_require__(/*! ../components/globals/intercept-console-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/globals/intercept-console-error.js\");\nconst _errorboundary = __webpack_require__(/*! ../components/error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\");\nfunction onCaughtError(err, errorInfo) {\n    var _errorInfo_errorBoundary;\n    const errorBoundaryComponent = (_errorInfo_errorBoundary = errorInfo.errorBoundary) == null ? void 0 : _errorInfo_errorBoundary.constructor;\n    let isImplicitErrorBoundary;\n    if (true) {\n        const { AppDevOverlayErrorBoundary } = __webpack_require__(/*! ../components/react-dev-overlay/app/app-dev-overlay-error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay-error-boundary.js\");\n        isImplicitErrorBoundary = errorBoundaryComponent === AppDevOverlayErrorBoundary;\n    }\n    isImplicitErrorBoundary = isImplicitErrorBoundary || errorBoundaryComponent === _errorboundary.ErrorBoundaryHandler && errorInfo.errorBoundary.props.errorComponent === _errorboundary.GlobalError;\n    if (isImplicitErrorBoundary) {\n        // We don't consider errors caught unless they're caught by an explicit error\n        // boundary. The built-in ones are considered implicit.\n        // This mimics how the same app would behave without Next.js.\n        return onUncaughtError(err, errorInfo);\n    }\n    // Skip certain custom errors which are not expected to be reported on client\n    if ((0, _bailouttocsr.isBailoutToCSRError)(err) || (0, _isnextroutererror.isNextRouterError)(err)) return;\n    if (true) {\n        var _errorInfo_componentStack;\n        const errorBoundaryName = (errorBoundaryComponent == null ? void 0 : errorBoundaryComponent.displayName) || (errorBoundaryComponent == null ? void 0 : errorBoundaryComponent.name) || 'Unknown';\n        const componentThatErroredFrame = errorInfo == null ? void 0 : (_errorInfo_componentStack = errorInfo.componentStack) == null ? void 0 : _errorInfo_componentStack.split('\\n')[1];\n        var // example 1: at Page (http://localhost:3000/_next/static/chunks/pages/index.js?ts=1631600000000:2:1)\n        // example 2: Page@http://localhost:3000/_next/static/chunks/pages/index.js?ts=1631600000000:2:1\n        _componentThatErroredFrame_match;\n        // Match chrome or safari stack trace\n        const matches = (_componentThatErroredFrame_match = componentThatErroredFrame == null ? void 0 : componentThatErroredFrame.match(/\\s+at (\\w+)\\s+|(\\w+)@/)) != null ? _componentThatErroredFrame_match : [];\n        const componentThatErroredName = matches[1] || matches[2] || 'Unknown';\n        // Create error location with errored component and error boundary, to match the behavior of default React onCaughtError handler.\n        const errorBoundaryMessage = \"It was handled by the <\" + errorBoundaryName + \"> error boundary.\";\n        const componentErrorMessage = componentThatErroredName ? \"The above error occurred in the <\" + componentThatErroredName + \"> component.\" : \"The above error occurred in one of your components.\";\n        const errorLocation = componentErrorMessage + \" \" + errorBoundaryMessage;\n        const stitchedError = (0, _stitchederror.getReactStitchedError)(err);\n        // TODO: change to passing down errorInfo later\n        // In development mode, pass along the component stack to the error\n        if (errorInfo.componentStack) {\n            ;\n            stitchedError._componentStack = errorInfo.componentStack;\n        }\n        // Log and report the error with location but without modifying the error stack\n        (0, _interceptconsoleerror.originConsoleError)('%o\\n\\n%s', err, errorLocation);\n        (0, _useerrorhandler.handleClientError)(stitchedError);\n    } else {}\n}\nfunction onUncaughtError(err, errorInfo) {\n    // Skip certain custom errors which are not expected to be reported on client\n    if ((0, _bailouttocsr.isBailoutToCSRError)(err) || (0, _isnextroutererror.isNextRouterError)(err)) return;\n    if (true) {\n        const stitchedError = (0, _stitchederror.getReactStitchedError)(err);\n        // TODO: change to passing down errorInfo later\n        // In development mode, pass along the component stack to the error\n        if (errorInfo.componentStack) {\n            ;\n            stitchedError._componentStack = errorInfo.componentStack;\n        }\n        // TODO: Add an adendum to the overlay telling people about custom error boundaries.\n        (0, _reportglobalerror.reportGlobalError)(stitchedError);\n    } else {}\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=error-boundary-callbacks.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/error-boundary-callbacks.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/on-recoverable-error.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next/dist/client/react-client-callbacks/on-recoverable-error.js ***!
  \**************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("// This module can be shared between both pages router and app router\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"onRecoverableError\", ({\n    enumerable: true,\n    get: function() {\n        return onRecoverableError;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _bailouttocsr = __webpack_require__(/*! ../../shared/lib/lazy-dynamic/bailout-to-csr */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/bailout-to-csr.js\");\nconst _reportglobalerror = __webpack_require__(/*! ./report-global-error */ \"(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/report-global-error.js\");\nconst _stitchederror = __webpack_require__(/*! ../components/errors/stitched-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/stitched-error.js\");\nconst _iserror = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../lib/is-error */ \"(app-pages-browser)/./node_modules/next/dist/lib/is-error.js\"));\nconst onRecoverableError = (error, errorInfo)=>{\n    // x-ref: https://github.com/facebook/react/pull/28736\n    const cause = (0, _iserror.default)(error) && 'cause' in error ? error.cause : error;\n    const stitchedError = (0, _stitchederror.getReactStitchedError)(cause);\n    // In development mode, pass along the component stack to the error\n    if ( true && errorInfo.componentStack) {\n        ;\n        stitchedError._componentStack = errorInfo.componentStack;\n    }\n    // Skip certain custom errors which are not expected to be reported on client\n    if ((0, _bailouttocsr.isBailoutToCSRError)(cause)) return;\n    (0, _reportglobalerror.reportGlobalError)(stitchedError);\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=on-recoverable-error.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/on-recoverable-error.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js ***!
  \**********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    PathParamsContext: function() {\n        return PathParamsContext;\n    },\n    PathnameContext: function() {\n        return PathnameContext;\n    },\n    SearchParamsContext: function() {\n        return SearchParamsContext;\n    }\n});\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst SearchParamsContext = (0, _react.createContext)(null);\nconst PathnameContext = (0, _react.createContext)(null);\nconst PathParamsContext = (0, _react.createContext)(null);\nif (true) {\n    SearchParamsContext.displayName = 'SearchParamsContext';\n    PathnameContext.displayName = 'PathnameContext';\n    PathParamsContext.displayName = 'PathParamsContext';\n} //# sourceMappingURL=hooks-client-context.shared-runtime.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9ob29rcy1jbGllbnQtY29udGV4dC5zaGFyZWQtcnVudGltZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7SUFPYUEsaUJBQWlCO2VBQWpCQTs7SUFEQUMsZUFBZTtlQUFmQTs7SUFEQUMsbUJBQW1CO2VBQW5CQTs7O21DQUhpQjtBQUd2QixNQUFNQSxzQkFBc0JDLENBQUFBLEdBQUFBLE9BQUFBLGFBQUFBLEVBQXNDO0FBQ2xFLE1BQU1GLGtCQUFrQkUsQ0FBQUEsR0FBQUEsT0FBQUEsYUFBQUEsRUFBNkI7QUFDckQsTUFBTUgsb0JBQW9CRyxDQUFBQSxHQUFBQSxPQUFBQSxhQUFBQSxFQUE2QjtBQUU5RCxJQUFJQyxJQUFvQixFQUFtQjtJQUN6Q0Ysb0JBQW9CSyxXQUFXLEdBQUc7SUFDbENOLGdCQUFnQk0sV0FBVyxHQUFHO0lBQzlCUCxrQkFBa0JPLFdBQVcsR0FBRztBQUNsQyIsInNvdXJjZXMiOlsiQzpcXHNyY1xcc2hhcmVkXFxsaWJcXGhvb2tzLWNsaWVudC1jb250ZXh0LnNoYXJlZC1ydW50aW1lLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyBjcmVhdGVDb250ZXh0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgdHlwZSB7IFBhcmFtcyB9IGZyb20gJy4uLy4uL3NlcnZlci9yZXF1ZXN0L3BhcmFtcydcblxuZXhwb3J0IGNvbnN0IFNlYXJjaFBhcmFtc0NvbnRleHQgPSBjcmVhdGVDb250ZXh0PFVSTFNlYXJjaFBhcmFtcyB8IG51bGw+KG51bGwpXG5leHBvcnQgY29uc3QgUGF0aG5hbWVDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxzdHJpbmcgfCBudWxsPihudWxsKVxuZXhwb3J0IGNvbnN0IFBhdGhQYXJhbXNDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxQYXJhbXMgfCBudWxsPihudWxsKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICBTZWFyY2hQYXJhbXNDb250ZXh0LmRpc3BsYXlOYW1lID0gJ1NlYXJjaFBhcmFtc0NvbnRleHQnXG4gIFBhdGhuYW1lQ29udGV4dC5kaXNwbGF5TmFtZSA9ICdQYXRobmFtZUNvbnRleHQnXG4gIFBhdGhQYXJhbXNDb250ZXh0LmRpc3BsYXlOYW1lID0gJ1BhdGhQYXJhbXNDb250ZXh0J1xufVxuIl0sIm5hbWVzIjpbIlBhdGhQYXJhbXNDb250ZXh0IiwiUGF0aG5hbWVDb250ZXh0IiwiU2VhcmNoUGFyYW1zQ29udGV4dCIsImNyZWF0ZUNvbnRleHQiLCJwcm9jZXNzIiwiZW52IiwiTk9ERV9FTlYiLCJkaXNwbGF5TmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js\n"));

/***/ })

});