"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/ai-summarization/page",{

/***/ "(app-pages-browser)/./src/app/admin/AiSummarizationPanel.tsx":
/*!************************************************!*\
  !*** ./src/app/admin/AiSummarizationPanel.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AiSummarizationPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _barrel_optimize_names_Check_CheckCheck_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CheckCheck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_CheckCheck_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CheckCheck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-check.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction PageList(param) {\n    let { pages, selected, onSelect } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full mb-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \" font-semibold text-brand-blue/[85%] mt-2 mb-4\",\n                children: [\n                    \"Medical Course Pages (\",\n                    pages.length,\n                    \" pages)\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                className: \"h-full space-y-3 overflow-y-auto max-h-[calc(100vh-5%)] pr-2 custom-scrollbar\",\n                children: pages.map((p, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"w-full text-left px-5 py-3 rounded-xl border transition-all duration-200 flex flex-col gap-1 shadow-sm \\n                \".concat(selected === idx ? \"border-orange-500 bg-orange-50 text-orange-800 shadow-md\" : \"border-gray-200 bg-white hover:bg-orange-50 hover:border-orange-300\", \"\\n              \"),\n                            onClick: ()=>onSelect(idx),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-bold text-lg\",\n                                    children: [\n                                        \"Page \",\n                                        p.page\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600 truncate\",\n                                    children: p.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"inline-block mt-1 text-xs px-3 py-1 rounded-full font-medium \\n                \".concat(p.status === \"Approved\" ? \"bg-green-100 text-green-700\" : \"bg-orange-100 text-orange-600\", \"\\n              \"),\n                                    children: p.status\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 13\n                        }, this)\n                    }, p.page, false, {\n                        fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n_c = PageList;\nfunction SummaryPanel(param) {\n    let { page, onChangeSummary, onApprove, onRegenerate, onCustomPrompt, onViewOriginal, onApproveAll, approvedCount, totalCount, pages, selected, onSelect } = param;\n    const progressPercentage = totalCount > 0 ? approvedCount / totalCount * 100 : 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 flex flex-col bg-white rounded-2xl shadow-xl px-4 py-4 border border-orange-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-brand-blue flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-7 h-7 text-orange-500\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: \"2\",\n                                    d: \"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-brand-blue\",\n                                children: \"AI-Powered Medical Summarization\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-md font-medium text-brand-blue-300\",\n                        children: [\n                            \"Progress: \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-orange-600\",\n                                children: approvedCount\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 76\n                            }, this),\n                            \" of \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-brand-blue-300\",\n                                children: totalCount\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 136\n                            }, this),\n                            \" approved\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full bg-brand-blue-300/[20%] rounded-full h-2.5 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-green-500 h-2.5 rounded-full transition-all duration-500 ease-out\",\n                    style: {\n                        width: \"\".concat(progressPercentage, \"%\")\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-3 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-bold text-brand-blue-300\",\n                                            children: page.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 flex justify-end gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.Tooltip, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipTrigger, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-100 text-sm font-medium transition flex items-center gap-1 shadow-sm\",\n                                                                onClick: onRegenerate,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 text-gray-600\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"2\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M4 4v5h.582M20 20v-5h-.581\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                            lineNumber: 68,\n                                                                            columnNumber: 130\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M5.077 19A9 9 0 1 0 6.5 6.5L4 9.5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                            lineNumber: 68,\n                                                                            columnNumber: 168\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                    lineNumber: 68,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                lineNumber: 67,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                            lineNumber: 66,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipContent, {\n                                                            variant: \"brand\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"Regenerate\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                lineNumber: 72,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                            lineNumber: 71,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                    lineNumber: 65,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.Tooltip, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipTrigger, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-100 text-sm font-medium transition flex items-center gap-1 shadow-sm\",\n                                                                onClick: onCustomPrompt,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 text-gray-600\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"2\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M16.862 3.487a2.6 2.6 0 1 1 3.677 3.677L7.5 20.205l-4.5 1 1-4.5L16.862 3.487Z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                            lineNumber: 78,\n                                                                            columnNumber: 126\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M15 6.5 17.5 9\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                            lineNumber: 78,\n                                                                            columnNumber: 215\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                    lineNumber: 78,\n                                                                    columnNumber: 17\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                lineNumber: 77,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                            lineNumber: 76,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipContent, {\n                                                            variant: \"brand\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"Custom Prompt\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                lineNumber: 82,\n                                                                columnNumber: 20\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                            lineNumber: 81,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.Tooltip, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipTrigger, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-100 text-sm font-medium transition flex items-center gap-1 shadow-sm\",\n                                                                onClick: onViewOriginal,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 text-gray-600\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"2\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M1.5 12S5 5 12 5s10.5 7 10.5 7-3.5 7-10.5 7S1.5 12 1.5 12Z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                            lineNumber: 88,\n                                                                            columnNumber: 126\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                            cx: \"12\",\n                                                                            cy: \"12\",\n                                                                            r: \"3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                            lineNumber: 88,\n                                                                            columnNumber: 196\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                    lineNumber: 88,\n                                                                    columnNumber: 17\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                lineNumber: 87,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                            lineNumber: 86,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipContent, {\n                                                            variant: \"brand\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"View Original\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                lineNumber: 92,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                            lineNumber: 91,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                    lineNumber: 85,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 flex-1 flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"AI-Generated Summary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            className: \"w-full border border-gray-200 rounded-lg px-4 py-3 min-h-[150px] focus:ring-2 focus:ring-orange-200 focus:border-orange-300 transition-colors duration-200 resize-y flex-1\",\n                                            value: page.aiSummary,\n                                            onChange: (e)=>onChangeSummary(e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mt-4 text-xs text-gray-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-orange-50 text-orange-600 px-2 py-1 rounded-full font-medium\",\n                                                    children: \"Generated by Azure OpenAI GPT-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-brand-blue/[25%] text-brand-blue px-2 py-1 rounded-full font-medium\",\n                                                    children: \"Medical AI Model\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 flex-1 flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Original Content Preview\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 text-gray-700 text-sm min-h-[150px] overflow-y-auto flex-1 custom-scrollbar\",\n                                            children: page.original\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-3 mt-2 justify-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            variant: 'outline',\n                                            className: \"text-brand-blue border-brand-blue hover:bg-brand-blue hover:text-white px-6 py-3 rounded-lg font-semibold shadow-md hover:shadow-lg transition-all duration-200 flex items-center gap-2\",\n                                            onClick: onApprove,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCheck_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 15\n                                                }, this),\n                                                \"Approve\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            variant: 'outline',\n                                            className: \"text-brand-blue border-brand-blue hover:bg-brand-blue hover:text-white px-6 py-3 rounded-lg font-semibold shadow-md hover:shadow-lg transition-all duration-200 flex items-center gap-2\",\n                                            onClick: onApproveAll,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCheck_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 15\n                                                }, this),\n                                                \"Approve All (\",\n                                                pages.filter((p)=>p.status === \"Pending Review\").length,\n                                                \" remaining)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PageList, {\n                            pages: pages,\n                            selected: selected,\n                            onSelect: onSelect\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n_c1 = SummaryPanel;\nfunction AiSummarizationPanel() {\n    _s();\n    const [selected, setSelected] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(0);\n    const [pages, setPages] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [showPromptModal, setShowPromptModal] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [customPrompt, setCustomPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [courses, setCourses] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [selectedCourseId, setSelectedCourseId] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [showApproveAllModal, setShowApproveAllModal] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [selectedCourseBlobUrl, setSelectedCourseBlobUrl] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"AiSummarizationPanel.useEffect\": ()=>{\n            // Fetch courses on mount\n            fetch(\"http://172.24.175.70:5001\" + \"/api/courses\").then({\n                \"AiSummarizationPanel.useEffect\": (res)=>res.json()\n            }[\"AiSummarizationPanel.useEffect\"]).then({\n                \"AiSummarizationPanel.useEffect\": (data)=>setCourses(data)\n            }[\"AiSummarizationPanel.useEffect\"]);\n        }\n    }[\"AiSummarizationPanel.useEffect\"], []);\n    const handleCourseSelect = async (value)=>{\n        const courseId = value;\n        setSelectedCourseId(courseId);\n        setLoading(true);\n        const res = await fetch(\"http://172.24.175.70:5001\" + '/api/courses/' + courseId);\n        const data = await res.json();\n        // Map pdf_output to the format expected by the UI\n        const mappedPages = (data.pdf_output || []).map((p, idx)=>({\n                page: p.page || idx + 1,\n                title: \"Page \".concat(p.page || idx + 1),\n                status: \"Pending Review\",\n                aiSummary: p.imagetext || \"\",\n                original: p.imagetext || \"\",\n                imageassestid: p.imageassestid\n            }));\n        setPages(mappedPages);\n        setSelected(0);\n        setLoading(false);\n        setSelectedCourseBlobUrl(data.blob_url || null);\n    };\n    const handleChangeSummary = (val)=>{\n        setPages((pages)=>pages.map((p, idx)=>idx === selected ? {\n                    ...p,\n                    aiSummary: val\n                } : p));\n    };\n    const handleApprove = ()=>{\n        setPages((pages)=>pages.map((p, idx)=>idx === selected ? {\n                    ...p,\n                    status: \"Approved\"\n                } : p));\n    };\n    const handleApproveAll = ()=>{\n        setPages((pages)=>pages.map((p)=>({\n                    ...p,\n                    status: \"Approved\"\n                })));\n    };\n    const handleRegenerate = async ()=>{\n        const text = pages[selected].aiSummary;\n        try {\n            const res = await fetch(\"\".concat(\"http://172.24.175.70:5001\", \"/api/rephrase\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    text\n                })\n            });\n            const data = await res.json();\n            if (data && data.rephrased) {\n                setPages((pages)=>pages.map((p, idx)=>idx === selected ? {\n                            ...p,\n                            aiSummary: data.rephrased\n                        } : p));\n            } else if (typeof data === 'string') {\n                setPages((pages)=>pages.map((p, idx)=>idx === selected ? {\n                            ...p,\n                            aiSummary: data\n                        } : p));\n            }\n        } catch (err) {\n            alert('Failed to rephrase.');\n        }\n    };\n    const handleCustomPrompt = ()=>{\n        setShowPromptModal(true);\n    };\n    const handleViewOriginal = ()=>{\n        if (selectedCourseBlobUrl) {\n            window.open(selectedCourseBlobUrl, '_blank');\n        } else {\n            alert(\"No original content URL available.\");\n        }\n    };\n    const handlePromptCancel = ()=>{\n        setShowPromptModal(false);\n        setCustomPrompt(\"\");\n    };\n    const handlePromptGenerate = async ()=>{\n        const summaryText = pages[selected].aiSummary;\n        const promptText = customPrompt;\n        try {\n            const res = await fetch(\"\".concat(\"http://172.24.175.70:5002\", \"/api/customrephrase\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    text: summaryText,\n                    prompt: promptText\n                })\n            });\n            const data = await res.json();\n            if (data && data.result) {\n                setPages((pages)=>pages.map((p, idx)=>idx === selected ? {\n                            ...p,\n                            aiSummary: data.result\n                        } : p));\n            } else {\n                alert('Failed to get rephrased result.');\n            }\n        } catch (err) {\n            alert('Failed to get rephrased result.');\n        }\n        setShowPromptModal(false);\n        setCustomPrompt(\"\");\n    };\n    const handleApproveAllClick = ()=>{\n        setShowApproveAllModal(true);\n    };\n    const handleApproveAllConfirm = async ()=>{\n        setShowApproveAllModal(false);\n        handleApproveAll();\n        // Get user email from localStorage token\n        let userEmail = \"unknown\";\n        try {\n            const token = localStorage.getItem(\"token\");\n            if (token) {\n                const userStr = atob(token.split(\".\")[1]);\n                const user = JSON.parse(userStr);\n                userEmail = user.email || \"unknown\";\n            }\n        } catch (e) {}\n        // Send approved data to backend\n        try {\n            const res = await fetch(\"\".concat(\"http://172.24.175.70:5001\", \"/api/courses/\").concat(selectedCourseId, \"/approve\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    content: pages.map((p)=>({\n                            ...p,\n                            status: \"Approved\"\n                        })),\n                    approved_by: userEmail,\n                    comment: ''\n                })\n            });\n            if (!res.ok) {\n                alert('Failed to save approved content!');\n            } else {\n                setSuccessMessage('Content approved and saved!');\n                setTimeout(()=>setSuccessMessage(\"\"), 3000);\n            }\n        } catch (err) {\n            alert('Failed to save approved content!');\n        }\n    };\n    const handleApproveAllCancel = ()=>{\n        setShowApproveAllModal(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full px-4 py-4 flex flex-col items-stretch justify-start relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                className: \"flex gap-4 items-end py-4\",\n                onSubmit: (e)=>e.preventDefault(),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_2__.Label, {\n                            htmlFor: \"course\",\n                            className: \"py-2\",\n                            children: \"Course\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.Select, {\n                            value: selectedCourseId || '',\n                            onValueChange: (value)=>handleCourseSelect(value),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectTrigger, {\n                                    className: \"w-auto focus:ring-2 focus:ring-orange-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectValue, {\n                                        placeholder: \"Select Course\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectContent, {\n                                    children: courses.map((c)=>// <option key={c.id} value={c.id}>{c.title} ({c.domain})</option>\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                            value: c.id.toString(),\n                                            children: [\n                                                c.title,\n                                                \" (\",\n                                                c.domain,\n                                                \")\"\n                                            ]\n                                        }, c.id, true, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                    lineNumber: 296,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 295,\n                columnNumber: 9\n            }, this),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-lg text-orange-500\",\n                children: \"Loading course data...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 326,\n                columnNumber: 19\n            }, this),\n            !loading && pages.length > 0 && // <div className=\"w-full bg-white rounded-2xl shadow-lg p-8 mt-8\">\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SummaryPanel, {\n                page: pages[selected],\n                onChangeSummary: handleChangeSummary,\n                onApprove: handleApprove,\n                onRegenerate: handleRegenerate,\n                onCustomPrompt: handleCustomPrompt,\n                onViewOriginal: handleViewOriginal,\n                onApproveAll: handleApproveAllClick,\n                approvedCount: pages.filter((p)=>p.status === \"Approved\").length,\n                totalCount: pages.length,\n                pages: pages,\n                selected: selected,\n                onSelect: setSelected\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 329,\n                columnNumber: 11\n            }, this),\n            showPromptModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full animate-fade-in-up relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-bold text-gray-800 mb-4\",\n                            children: \"Custom AI Prompt\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            className: \"w-full border border-gray-300 rounded-lg p-3 min-h-[120px] focus:ring-2 focus:ring-orange-200 focus:border-orange-300 transition-colors duration-200 resize-y\",\n                            placeholder: \"Enter your custom prompt here to regenerate the summary...\",\n                            value: customPrompt,\n                            onChange: (e)=>setCustomPrompt(e.target.value)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-3 rounded-lg font-semibold transition-colors duration-200\",\n                                    onClick: handlePromptCancel,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200\",\n                                    onClick: handlePromptGenerate,\n                                    children: \"Generate\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 357,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                    lineNumber: 349,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 348,\n                columnNumber: 9\n            }, this),\n            showApproveAllModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-2xl p-8 max-w-sm w-full animate-fade-in-up relative text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-bold text-gray-800 mb-4\",\n                            children: \"Confirm Approval\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-6\",\n                            children: \"Are you sure you want to approve all pending summaries for this course? This action cannot be undone.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 380,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-3 rounded-lg font-semibold transition-colors duration-200\",\n                                    onClick: handleApproveAllCancel,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200\",\n                                    onClick: handleApproveAllConfirm,\n                                    children: \"Confirm Approve All\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 381,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                    lineNumber: 378,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 377,\n                columnNumber: 9\n            }, this),\n            successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-6 right-6 bg-green-500 text-white px-6 py-3 rounded-lg shadow-xl text-lg font-semibold animate-fade-in-right z-50\",\n                children: successMessage\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 400,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n        lineNumber: 292,\n        columnNumber: 5\n    }, this);\n}\n_s(AiSummarizationPanel, \"CO/z/i0KmRkbIZmMS/BiU85F0sQ=\");\n_c2 = AiSummarizationPanel;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"PageList\");\n$RefreshReg$(_c1, \"SummaryPanel\");\n$RefreshReg$(_c2, \"AiSummarizationPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvYWRtaW4vQWlTdW1tYXJpemF0aW9uUGFuZWwudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUNnRDtBQUNGO0FBQ3lEO0FBQ3JCO0FBQ2pDO0FBQ0w7QUFFNUMsU0FBU2MsU0FBUyxLQUFrQztRQUFsQyxFQUFFQyxLQUFLLEVBQUVDLFFBQVEsRUFBRUMsUUFBUSxFQUFPLEdBQWxDO0lBQ2hCLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0M7Z0JBQUdELFdBQVU7O29CQUFpRDtvQkFBdUJKLE1BQU1NLE1BQU07b0JBQUM7Ozs7Ozs7MEJBQ25HLDhEQUFDQztnQkFBR0gsV0FBVTswQkFDWEosTUFBTVEsR0FBRyxDQUFDLENBQUNDLEdBQVFDLG9CQUNsQiw4REFBQ0M7a0NBQ0MsNEVBQUNDOzRCQUNDUixXQUFXLDRIQUdpRSxPQUZ4RUgsYUFBYVMsTUFDWCw2REFDQSx1RUFBc0U7NEJBRTVFRyxTQUFTLElBQU1YLFNBQVNROzs4Q0FFeEIsOERBQUNQO29DQUFJQyxXQUFVOzt3Q0FBb0I7d0NBQU1LLEVBQUVLLElBQUk7Ozs7Ozs7OENBQy9DLDhEQUFDWDtvQ0FBSUMsV0FBVTs4Q0FBa0NLLEVBQUVNLEtBQUs7Ozs7Ozs4Q0FDeEQsOERBQUNDO29DQUFLWixXQUFXLGtGQUdxQixPQUZsQ0ssRUFBRVEsTUFBTSxLQUFLLGFBQ1gsZ0NBQ0EsaUNBQWdDOzhDQUVuQ1IsRUFBRVEsTUFBTTs7Ozs7Ozs7Ozs7O3VCQWhCTlIsRUFBRUssSUFBSTs7Ozs7Ozs7Ozs7Ozs7OztBQXdCekI7S0E5QlNmO0FBZ0NULFNBQVNtQixhQUFhLEtBQTJKO1FBQTNKLEVBQUVKLElBQUksRUFBRUssZUFBZSxFQUFFQyxTQUFTLEVBQUVDLFlBQVksRUFBRUMsY0FBYyxFQUFFQyxjQUFjLEVBQUVDLFlBQVksRUFBRUMsYUFBYSxFQUFFQyxVQUFVLEVBQUUxQixLQUFLLEVBQUVDLFFBQVEsRUFBRUMsUUFBUSxFQUFPLEdBQTNKO0lBQ3BCLE1BQU15QixxQkFBcUJELGFBQWEsSUFBSSxnQkFBaUJBLGFBQWMsTUFBTTtJQUVqRixxQkFDRSw4REFBQ3ZCO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUN3Qjt3QkFBR3hCLFdBQVU7OzBDQUNaLDhEQUFDeUI7Z0NBQUl6QixXQUFVO2dDQUEwQjBCLE1BQUs7Z0NBQU9DLFFBQU87Z0NBQWVDLFNBQVE7MENBQ2pGLDRFQUFDQztvQ0FBS0MsZUFBYztvQ0FBUUMsZ0JBQWU7b0NBQVFDLGFBQVk7b0NBQUlDLEdBQUU7Ozs7Ozs7Ozs7OzBDQUV2RSw4REFBQ3JCO2dDQUFLWixXQUFVOzBDQUFrQjs7Ozs7Ozs7Ozs7O2tDQUVwQyw4REFBQ0Q7d0JBQUlDLFdBQVU7OzRCQUEwQzswQ0FBVSw4REFBQ1k7Z0NBQUtaLFdBQVU7MENBQW1CcUI7Ozs7Ozs0QkFBcUI7MENBQUksOERBQUNUO2dDQUFLWixXQUFVOzBDQUF1QnNCOzs7Ozs7NEJBQWtCOzs7Ozs7Ozs7Ozs7OzBCQUUxTCw4REFBQ3ZCO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTtvQkFBdUVrQyxPQUFPO3dCQUFFQyxPQUFPLEdBQXNCLE9BQW5CWixvQkFBbUI7b0JBQUc7Ozs7Ozs7Ozs7OzBCQUdqSSw4REFBQ3hCO2dCQUFJQyxXQUFVOztrQ0FDZiw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDQzs0Q0FBR0QsV0FBVTtzREFBaUNVLEtBQUtDLEtBQUs7Ozs7OztzREFDekQsOERBQUNaOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ1osMkRBQU9BOztzRUFDTiw4REFBQ0Usa0VBQWNBOzREQUFDOEMsT0FBTztzRUFDckIsNEVBQUN2RCx5REFBTUE7Z0VBQUN3RCxTQUFRO2dFQUFVckMsV0FBVTtnRUFBK0lTLFNBQVNROzBFQUMxTCw0RUFBQ1E7b0VBQUl6QixXQUFVO29FQUF3QjBCLE1BQUs7b0VBQU9DLFFBQU87b0VBQWVLLGFBQVk7b0VBQUlKLFNBQVE7O3NGQUFZLDhEQUFDQzs0RUFBS0ksR0FBRTs7Ozs7O3NGQUE4Qiw4REFBQ0o7NEVBQUtJLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0VBRy9KLDhEQUFDNUMsa0VBQWNBOzREQUFDZ0QsU0FBUTtzRUFDdEIsNEVBQUNoQzswRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBR1AsOERBQUNqQiwyREFBT0E7O3NFQUNOLDhEQUFDRSxrRUFBY0E7NERBQUM4QyxPQUFPO3NFQUNyQiw0RUFBQ3ZELHlEQUFNQTtnRUFBQ3dELFNBQVE7Z0VBQVVyQyxXQUFVO2dFQUErSVMsU0FBU1M7MEVBQzlMLDRFQUFDTztvRUFBSXpCLFdBQVU7b0VBQXdCMEIsTUFBSztvRUFBT0MsUUFBTztvRUFBZUssYUFBWTtvRUFBSUosU0FBUTs7c0ZBQVksOERBQUNDOzRFQUFLSSxHQUFFOzs7Ozs7c0ZBQWlGLDhEQUFDSjs0RUFBS0ksR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzRUFHOU0sOERBQUM1QyxrRUFBY0E7NERBQUNnRCxTQUFRO3NFQUNyQiw0RUFBQ2hDOzBFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs4REFHUiw4REFBQ2pCLDJEQUFPQTs7c0VBQ04sOERBQUNFLGtFQUFjQTs0REFBQzhDLE9BQU87c0VBQ3JCLDRFQUFDdkQseURBQU1BO2dFQUFDd0QsU0FBUTtnRUFBVXJDLFdBQVU7Z0VBQStJUyxTQUFTVTswRUFDOUwsNEVBQUNNO29FQUFJekIsV0FBVTtvRUFBd0IwQixNQUFLO29FQUFPQyxRQUFPO29FQUFlSyxhQUFZO29FQUFJSixTQUFROztzRkFBWSw4REFBQ0M7NEVBQUtJLEdBQUU7Ozs7OztzRkFBOEQsOERBQUNLOzRFQUFPQyxJQUFHOzRFQUFLQyxJQUFHOzRFQUFLQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NFQUc3TSw4REFBQ3BELGtFQUFjQTs0REFBQ2dELFNBQVE7c0VBQ3RCLDRFQUFDaEM7MEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUtYLDhEQUFDTjtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUMwQzs0Q0FBTTFDLFdBQVU7c0RBQStDOzs7Ozs7c0RBQ2hFLDhEQUFDMkM7NENBQ0MzQyxXQUFVOzRDQUNWNEMsT0FBT2xDLEtBQUttQyxTQUFTOzRDQUNyQkMsVUFBVUMsQ0FBQUEsSUFBS2hDLGdCQUFnQmdDLEVBQUVDLE1BQU0sQ0FBQ0osS0FBSzs7Ozs7O3NEQUUvQyw4REFBQzdDOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ1k7b0RBQUtaLFdBQVU7OERBQWtFOzs7Ozs7OERBQ2xGLDhEQUFDWTtvREFBS1osV0FBVTs4REFBeUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FHN0YsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQzBDOzRDQUFNMUMsV0FBVTtzREFBK0M7Ozs7OztzREFDaEUsOERBQUNEOzRDQUFJQyxXQUFVO3NEQUNaVSxLQUFLdUMsUUFBUTs7Ozs7Ozs7Ozs7OzhDQUdsQiw4REFBQ2xEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ25CLHlEQUFNQTs0Q0FBQ3dELFNBQVM7NENBQVdyQyxXQUFVOzRDQUEwTFMsU0FBU087OzhEQUN2Tyw4REFBQ3pCLDRGQUFLQTtvREFBQ1MsV0FBVTs7Ozs7O2dEQUFZOzs7Ozs7O3NEQUcvQiw4REFBQ25CLHlEQUFNQTs0Q0FBQ3dELFNBQVM7NENBQVdyQyxXQUFVOzRDQUEwTFMsU0FBU1c7OzhEQUN2Tyw4REFBQzVCLDRGQUFVQTtvREFBQ1EsV0FBVTs7Ozs7O2dEQUFZO2dEQUNwQkosTUFBTXNELE1BQU0sQ0FBQyxDQUFDN0MsSUFBV0EsRUFBRVEsTUFBTSxLQUFLLGtCQUFrQlgsTUFBTTtnREFBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUtyRiw4REFBQ0g7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNMOzRCQUFTQyxPQUFPQTs0QkFBT0MsVUFBVUE7NEJBQVVDLFVBQVVBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVk5RDtNQW5HU2dCO0FBcUdNLFNBQVNxQzs7SUFDdEIsTUFBTSxDQUFDdEQsVUFBVXVELFlBQVksR0FBRzNELCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQ0csT0FBT3lELFNBQVMsR0FBRzVELCtDQUFRQSxDQUFRLEVBQUU7SUFDNUMsTUFBTSxDQUFDNkQsaUJBQWlCQyxtQkFBbUIsR0FBRzlELCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQytELGNBQWNDLGdCQUFnQixHQUFHaEUsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDaUUsU0FBU0MsV0FBVyxHQUFHbEUsK0NBQVFBLENBQVEsRUFBRTtJQUNoRCxNQUFNLENBQUNtRSxrQkFBa0JDLG9CQUFvQixHQUFHcEUsK0NBQVFBLENBQWdCO0lBQ3hFLE1BQU0sQ0FBQ3FFLFNBQVNDLFdBQVcsR0FBR3RFLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ3VFLHFCQUFxQkMsdUJBQXVCLEdBQUd4RSwrQ0FBUUEsQ0FBQztJQUMvRCxNQUFNLENBQUN5RSxnQkFBZ0JDLGtCQUFrQixHQUFHMUUsK0NBQVFBLENBQUM7SUFDckQsTUFBTSxDQUFDMkUsdUJBQXVCQyx5QkFBeUIsR0FBRzVFLCtDQUFRQSxDQUFnQjtJQUVsRkMsZ0RBQVNBOzBDQUFDO1lBQ1IseUJBQXlCO1lBQ3pCNEUsTUFBTUMsMkJBQWlDLEdBQUMsZ0JBQ3JDRyxJQUFJO2tEQUFDQyxDQUFBQSxNQUFPQSxJQUFJQyxJQUFJO2lEQUNwQkYsSUFBSTtrREFBQ0csQ0FBQUEsT0FBUWxCLFdBQVdrQjs7UUFDN0I7eUNBQUcsRUFBRTtJQUVMLE1BQU1DLHFCQUFxQixPQUFPbEM7UUFDaEMsTUFBTW1DLFdBQVduQztRQUNqQmlCLG9CQUFvQmtCO1FBQ3BCaEIsV0FBVztRQUNYLE1BQU1ZLE1BQU0sTUFBTUwsTUFBTUMsMkJBQWlDLEdBQUMsa0JBQWdCUTtRQUMxRSxNQUFNRixPQUFPLE1BQU1GLElBQUlDLElBQUk7UUFDM0Isa0RBQWtEO1FBQ2xELE1BQU1JLGNBQWMsQ0FBQ0gsS0FBS0ksVUFBVSxJQUFJLEVBQUUsRUFBRTdFLEdBQUcsQ0FBQyxDQUFDQyxHQUFRQyxNQUFpQjtnQkFDeEVJLE1BQU1MLEVBQUVLLElBQUksSUFBSUosTUFBTTtnQkFDdEJLLE9BQU8sUUFBMEIsT0FBbEJOLEVBQUVLLElBQUksSUFBSUosTUFBTTtnQkFDL0JPLFFBQVE7Z0JBQ1JnQyxXQUFXeEMsRUFBRTZFLFNBQVMsSUFBSTtnQkFDMUJqQyxVQUFVNUMsRUFBRTZFLFNBQVMsSUFBSTtnQkFDekJDLGVBQWU5RSxFQUFFOEUsYUFBYTtZQUNoQztRQUNBOUIsU0FBUzJCO1FBQ1Q1QixZQUFZO1FBQ1pXLFdBQVc7UUFDWE0seUJBQXlCUSxLQUFLTyxRQUFRLElBQUk7SUFDNUM7SUFFQSxNQUFNQyxzQkFBc0IsQ0FBQ0M7UUFDM0JqQyxTQUFTekQsQ0FBQUEsUUFBU0EsTUFBTVEsR0FBRyxDQUFDLENBQUNDLEdBQUdDLE1BQVFBLFFBQVFULFdBQVc7b0JBQUUsR0FBR1EsQ0FBQztvQkFBRXdDLFdBQVd5QztnQkFBSSxJQUFJakY7SUFDeEY7SUFDQSxNQUFNa0YsZ0JBQWdCO1FBQ3BCbEMsU0FBU3pELENBQUFBLFFBQVNBLE1BQU1RLEdBQUcsQ0FBQyxDQUFDQyxHQUFHQyxNQUFRQSxRQUFRVCxXQUFXO29CQUFFLEdBQUdRLENBQUM7b0JBQUVRLFFBQVE7Z0JBQVcsSUFBSVI7SUFDNUY7SUFDQSxNQUFNbUYsbUJBQW1CO1FBQ3ZCbkMsU0FBU3pELENBQUFBLFFBQVNBLE1BQU1RLEdBQUcsQ0FBQ0MsQ0FBQUEsSUFBTTtvQkFBRSxHQUFHQSxDQUFDO29CQUFFUSxRQUFRO2dCQUFXO0lBQy9EO0lBQ0EsTUFBTTRFLG1CQUFtQjtRQUN2QixNQUFNQyxPQUFPOUYsS0FBSyxDQUFDQyxTQUFTLENBQUNnRCxTQUFTO1FBQ3RDLElBQUk7WUFDRixNQUFNOEIsTUFBTSxNQUFNTCxNQUFNLEdBQXFDLE9BQWxDQywyQkFBaUMsRUFBQyxrQkFBZ0I7Z0JBQzNFb0IsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO2dCQUNBQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7b0JBQUVMO2dCQUFLO1lBQzlCO1lBQ0EsTUFBTWIsT0FBTyxNQUFNRixJQUFJQyxJQUFJO1lBQzNCLElBQUlDLFFBQVFBLEtBQUttQixTQUFTLEVBQUU7Z0JBQzFCM0MsU0FBU3pELENBQUFBLFFBQVNBLE1BQU1RLEdBQUcsQ0FBQyxDQUFDQyxHQUFHQyxNQUFRQSxRQUFRVCxXQUFXOzRCQUFFLEdBQUdRLENBQUM7NEJBQUV3QyxXQUFXZ0MsS0FBS21CLFNBQVM7d0JBQUMsSUFBSTNGO1lBQ25HLE9BQU8sSUFBSSxPQUFPd0UsU0FBUyxVQUFVO2dCQUNuQ3hCLFNBQVN6RCxDQUFBQSxRQUFTQSxNQUFNUSxHQUFHLENBQUMsQ0FBQ0MsR0FBR0MsTUFBUUEsUUFBUVQsV0FBVzs0QkFBRSxHQUFHUSxDQUFDOzRCQUFFd0MsV0FBV2dDO3dCQUFLLElBQUl4RTtZQUN6RjtRQUNGLEVBQUUsT0FBTzRGLEtBQUs7WUFDWkMsTUFBTTtRQUNSO0lBQ0Y7SUFDQSxNQUFNQyxxQkFBcUI7UUFDekI1QyxtQkFBbUI7SUFDckI7SUFDQSxNQUFNNkMscUJBQXFCO1FBQ3pCLElBQUloQyx1QkFBdUI7WUFDekJpQyxPQUFPQyxJQUFJLENBQUNsQyx1QkFBdUI7UUFDckMsT0FBTztZQUNMOEIsTUFBTTtRQUNSO0lBQ0Y7SUFDQSxNQUFNSyxxQkFBcUI7UUFDekJoRCxtQkFBbUI7UUFDbkJFLGdCQUFnQjtJQUNsQjtJQUNBLE1BQU0rQyx1QkFBdUI7UUFDM0IsTUFBTUMsY0FBYzdHLEtBQUssQ0FBQ0MsU0FBUyxDQUFDZ0QsU0FBUztRQUM3QyxNQUFNNkQsYUFBYWxEO1FBQ25CLElBQUk7WUFDRixNQUFNbUIsTUFBTSxNQUFNTCxNQUFNLEdBQThDLE9BQTNDQywyQkFBMEMsRUFBQyx3QkFBc0I7Z0JBQzFGb0IsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO2dCQUNBQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7b0JBQUVMLE1BQU1lO29CQUFhRyxRQUFRRjtnQkFBVztZQUMvRDtZQUNBLE1BQU03QixPQUFPLE1BQU1GLElBQUlDLElBQUk7WUFDM0IsSUFBSUMsUUFBUUEsS0FBS2dDLE1BQU0sRUFBRTtnQkFDdkJ4RCxTQUFTekQsQ0FBQUEsUUFBU0EsTUFBTVEsR0FBRyxDQUFDLENBQUNDLEdBQUdDLE1BQVFBLFFBQVFULFdBQVc7NEJBQUUsR0FBR1EsQ0FBQzs0QkFBRXdDLFdBQVdnQyxLQUFLZ0MsTUFBTTt3QkFBQyxJQUFJeEc7WUFDaEcsT0FBTztnQkFDTDZGLE1BQU07WUFDUjtRQUNGLEVBQUUsT0FBT0QsS0FBSztZQUNaQyxNQUFNO1FBQ1I7UUFDQTNDLG1CQUFtQjtRQUNuQkUsZ0JBQWdCO0lBQ2xCO0lBQ0EsTUFBTXFELHdCQUF3QjtRQUM1QjdDLHVCQUF1QjtJQUN6QjtJQUNBLE1BQU04QywwQkFBMEI7UUFDOUI5Qyx1QkFBdUI7UUFDdkJ1QjtRQUVBLHlDQUF5QztRQUN6QyxJQUFJd0IsWUFBWTtRQUNoQixJQUFJO1lBQ0YsTUFBTUMsUUFBUUMsYUFBYUMsT0FBTyxDQUFDO1lBQ25DLElBQUlGLE9BQU87Z0JBQ1QsTUFBTUcsVUFBVUMsS0FBS0osTUFBTUssS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO2dCQUN4QyxNQUFNQyxPQUFPekIsS0FBSzBCLEtBQUssQ0FBQ0o7Z0JBQ3hCSixZQUFZTyxLQUFLRSxLQUFLLElBQUk7WUFDNUI7UUFDRixFQUFFLFVBQU0sQ0FBQztRQUVULGdDQUFnQztRQUNoQyxJQUFJO1lBQ0YsTUFBTTlDLE1BQU0sTUFBTUwsTUFBTSxHQUFvRFYsT0FBakRXLDJCQUFpQyxFQUFDLGlCQUFnQyxPQUFqQlgsa0JBQWlCLGFBQVc7Z0JBQ3RHK0IsUUFBUTtnQkFDUkMsU0FBUztvQkFBRSxnQkFBZ0I7Z0JBQW1CO2dCQUM5Q0MsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO29CQUNuQjJCLFNBQVM5SCxNQUFNUSxHQUFHLENBQUNDLENBQUFBLElBQU07NEJBQUUsR0FBR0EsQ0FBQzs0QkFBRVEsUUFBUTt3QkFBVztvQkFDcEQ4RyxhQUFhWDtvQkFDYlksU0FBUztnQkFDWDtZQUNGO1lBQ0EsSUFBSSxDQUFDakQsSUFBSWtELEVBQUUsRUFBRTtnQkFDWDNCLE1BQU07WUFDUixPQUFPO2dCQUNML0Isa0JBQWtCO2dCQUNsQjJELFdBQVcsSUFBTTNELGtCQUFrQixLQUFLO1lBQzFDO1FBQ0YsRUFBRSxPQUFPOEIsS0FBSztZQUNaQyxNQUFNO1FBQ1I7SUFDRjtJQUNBLE1BQU02Qix5QkFBeUI7UUFDN0I5RCx1QkFBdUI7SUFDekI7SUFFQSxxQkFDRSw4REFBQ2xFO1FBQUlDLFdBQVU7OzBCQUdYLDhEQUFDZ0k7Z0JBQUtoSSxXQUFVO2dCQUE0QmlJLFVBQVVsRixDQUFBQSxJQUFLQSxFQUFFbUYsY0FBYzswQkFDekUsNEVBQUNuSTtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNsQix1REFBS0E7NEJBQUNxSixTQUFROzRCQUFTbkksV0FBVTtzQ0FBTzs7Ozs7O3NDQUN6Qyw4REFBQ2pCLHlEQUFNQTs0QkFDSDZELE9BQU9nQixvQkFBb0I7NEJBQzNCd0UsZUFBZSxDQUFDeEYsUUFBVWtDLG1CQUFtQmxDOzs4Q0FFN0MsOERBQUMxRCxnRUFBYUE7b0NBQUNjLFdBQVU7OENBQ3ZCLDRFQUFDYiw4REFBV0E7d0NBQUNrSixhQUFZOzs7Ozs7Ozs7Ozs4Q0FFM0IsOERBQUNySixnRUFBYUE7OENBQ1gwRSxRQUFRdEQsR0FBRyxDQUFDLENBQUNrSSxJQUNoQixrRUFBa0U7c0RBQ2hFLDhEQUFDckosNkRBQVVBOzRDQUFZMkQsT0FBTzBGLEVBQUVDLEVBQUUsQ0FBQ0MsUUFBUTs7Z0RBQUtGLEVBQUUzSCxLQUFLO2dEQUFDO2dEQUFHMkgsRUFBRUcsTUFBTTtnREFBQzs7MkNBQW5ESCxFQUFFQyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFrQmhDekUseUJBQVcsOERBQUMvRDtnQkFBSUMsV0FBVTswQkFBc0M7Ozs7OztZQUNoRSxDQUFDOEQsV0FBV2xFLE1BQU1NLE1BQU0sR0FBRyxLQUMxQixtRUFBbUU7MEJBQ2pFLDhEQUFDWTtnQkFDQ0osTUFBTWQsS0FBSyxDQUFDQyxTQUFTO2dCQUNyQmtCLGlCQUFpQnNFO2dCQUNqQnJFLFdBQVd1RTtnQkFDWHRFLGNBQWN3RTtnQkFDZHZFLGdCQUFnQmlGO2dCQUNoQmhGLGdCQUFnQmlGO2dCQUNoQmhGLGNBQWMwRjtnQkFDZHpGLGVBQWV6QixNQUFNc0QsTUFBTSxDQUFDLENBQUM3QyxJQUFXQSxFQUFFUSxNQUFNLEtBQUssWUFBWVgsTUFBTTtnQkFDdkVvQixZQUFZMUIsTUFBTU0sTUFBTTtnQkFDeEJOLE9BQU9BO2dCQUNQQyxVQUFVQTtnQkFDVkMsVUFBVXNEOzs7Ozs7WUFNZkUsaUNBQ0MsOERBQUN2RDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDQzs0QkFBR0QsV0FBVTtzQ0FBdUM7Ozs7OztzQ0FDckQsOERBQUMyQzs0QkFDQzNDLFdBQVU7NEJBQ1ZxSSxhQUFZOzRCQUNaekYsT0FBT1k7NEJBQ1BWLFVBQVUsQ0FBQ0MsSUFBTVUsZ0JBQWdCVixFQUFFQyxNQUFNLENBQUNKLEtBQUs7Ozs7OztzQ0FFakQsOERBQUM3Qzs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNRO29DQUNDUixXQUFVO29DQUNWUyxTQUFTOEY7OENBQ1Y7Ozs7Ozs4Q0FHRCw4REFBQy9GO29DQUNDUixXQUFVO29DQUNWUyxTQUFTK0Y7OENBQ1Y7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBU1J4QyxxQ0FDQyw4REFBQ2pFO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNDOzRCQUFHRCxXQUFVO3NDQUF1Qzs7Ozs7O3NDQUNyRCw4REFBQ0s7NEJBQUVMLFdBQVU7c0NBQXFCOzs7Ozs7c0NBQ2xDLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNRO29DQUNDUixXQUFVO29DQUNWUyxTQUFTc0g7OENBQ1Y7Ozs7Ozs4Q0FHRCw4REFBQ3ZIO29DQUNDUixXQUFVO29DQUNWUyxTQUFTc0c7OENBQ1Y7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBUVI3QyxnQ0FDQyw4REFBQ25FO2dCQUFJQyxXQUFVOzBCQUNaa0U7Ozs7Ozs7Ozs7OztBQUtYO0dBeFF3QmY7TUFBQUEiLCJzb3VyY2VzIjpbIkM6XFxQcm9qZWN0c1xcQUktVHJhaW5lcjJcXGZyb250ZW5kXFxzcmNcXGFwcFxcYWRtaW5cXEFpU3VtbWFyaXphdGlvblBhbmVsLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcclxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9idXR0b25cIjtcclxuaW1wb3J0IHsgTGFiZWwgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2xhYmVsXCI7XHJcbmltcG9ydCB7IFNlbGVjdCwgU2VsZWN0Q29udGVudCwgU2VsZWN0SXRlbSwgU2VsZWN0VHJpZ2dlciwgU2VsZWN0VmFsdWUgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3NlbGVjdFwiO1xyXG5pbXBvcnQgeyBUb29sdGlwLCBUb29sdGlwQ29udGVudCwgVG9vbHRpcFRyaWdnZXIgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3Rvb2x0aXBcIjtcclxuaW1wb3J0IHsgQ2hlY2ssIENoZWNrQ2hlY2sgfSBmcm9tIFwibHVjaWRlLXJlYWN0XCI7XHJcbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tIFwicmVhY3RcIjtcclxuXHJcbmZ1bmN0aW9uIFBhZ2VMaXN0KHsgcGFnZXMsIHNlbGVjdGVkLCBvblNlbGVjdCB9OiBhbnkpIHtcclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgbWItOFwiPlxyXG4gICAgICA8aDMgY2xhc3NOYW1lPVwiIGZvbnQtc2VtaWJvbGQgdGV4dC1icmFuZC1ibHVlL1s4NSVdIG10LTIgbWItNFwiPk1lZGljYWwgQ291cnNlIFBhZ2VzICh7cGFnZXMubGVuZ3RofSBwYWdlcyk8L2gzPlxyXG4gICAgICA8dWwgY2xhc3NOYW1lPVwiaC1mdWxsIHNwYWNlLXktMyBvdmVyZmxvdy15LWF1dG8gbWF4LWgtW2NhbGMoMTAwdmgtNSUpXSBwci0yIGN1c3RvbS1zY3JvbGxiYXJcIj5cclxuICAgICAgICB7cGFnZXMubWFwKChwOiBhbnksIGlkeDogbnVtYmVyKSA9PiAoXHJcbiAgICAgICAgICA8bGkga2V5PXtwLnBhZ2V9PlxyXG4gICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy1mdWxsIHRleHQtbGVmdCBweC01IHB5LTMgcm91bmRlZC14bCBib3JkZXIgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGZsZXggZmxleC1jb2wgZ2FwLTEgc2hhZG93LXNtIFxyXG4gICAgICAgICAgICAgICAgJHtzZWxlY3RlZCA9PT0gaWR4IFxyXG4gICAgICAgICAgICAgICAgICA/IFwiYm9yZGVyLW9yYW5nZS01MDAgYmctb3JhbmdlLTUwIHRleHQtb3JhbmdlLTgwMCBzaGFkb3ctbWRcIiBcclxuICAgICAgICAgICAgICAgICAgOiBcImJvcmRlci1ncmF5LTIwMCBiZy13aGl0ZSBob3ZlcjpiZy1vcmFuZ2UtNTAgaG92ZXI6Ym9yZGVyLW9yYW5nZS0zMDBcIn1cclxuICAgICAgICAgICAgICBgfVxyXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IG9uU2VsZWN0KGlkeCl9XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtYm9sZCB0ZXh0LWxnXCI+UGFnZSB7cC5wYWdlfTwvZGl2PlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwIHRydW5jYXRlXCI+e3AudGl0bGV9PC9kaXY+XHJcbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgaW5saW5lLWJsb2NrIG10LTEgdGV4dC14cyBweC0zIHB5LTEgcm91bmRlZC1mdWxsIGZvbnQtbWVkaXVtIFxyXG4gICAgICAgICAgICAgICAgJHtwLnN0YXR1cyA9PT0gXCJBcHByb3ZlZFwiIFxyXG4gICAgICAgICAgICAgICAgICA/IFwiYmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tNzAwXCIgXHJcbiAgICAgICAgICAgICAgICAgIDogXCJiZy1vcmFuZ2UtMTAwIHRleHQtb3JhbmdlLTYwMFwifVxyXG4gICAgICAgICAgICAgIGB9PlxyXG4gICAgICAgICAgICAgICAge3Auc3RhdHVzfVxyXG4gICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICA8L2xpPlxyXG4gICAgICAgICkpfVxyXG4gICAgICA8L3VsPlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufVxyXG5cclxuZnVuY3Rpb24gU3VtbWFyeVBhbmVsKHsgcGFnZSwgb25DaGFuZ2VTdW1tYXJ5LCBvbkFwcHJvdmUsIG9uUmVnZW5lcmF0ZSwgb25DdXN0b21Qcm9tcHQsIG9uVmlld09yaWdpbmFsLCBvbkFwcHJvdmVBbGwsIGFwcHJvdmVkQ291bnQsIHRvdGFsQ291bnQsIHBhZ2VzLCBzZWxlY3RlZCwgb25TZWxlY3QgfTogYW55KSB7XHJcbiAgY29uc3QgcHJvZ3Jlc3NQZXJjZW50YWdlID0gdG90YWxDb3VudCA+IDAgPyAoYXBwcm92ZWRDb3VudCAvIHRvdGFsQ291bnQpICogMTAwIDogMDtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIGZsZXggZmxleC1jb2wgYmctd2hpdGUgcm91bmRlZC0yeGwgc2hhZG93LXhsIHB4LTQgcHktNCBib3JkZXIgYm9yZGVyLW9yYW5nZS0xMDBcIj5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNFwiPlxyXG4gICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1icmFuZC1ibHVlIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNyBoLTcgdGV4dC1vcmFuZ2UtNTAwXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XHJcbiAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD1cIjJcIiBkPVwiTTkgNUg3YTIgMiAwIDAwLTIgMnYxMmEyIDIgMCAwMDIgMmgxMGEyIDIgMCAwMDItMlY3YTIgMiAwIDAwLTItMmgtMk05IDVhMiAyIDAgMDAyIDJoMmEyIDIgMCAwMDItMk05IDVhMiAyIDAgMDEyLTJoMmEyIDIgMCAwMTIgMm0tMyA3aDNtLTMgNGgzbS02LTRoLjAxTTkgMTZoLjAxXCIgLz5cclxuICAgICAgICAgIDwvc3ZnPlxyXG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1icmFuZC1ibHVlXCI+QUktUG93ZXJlZCBNZWRpY2FsIFN1bW1hcml6YXRpb248L3NwYW4+XHJcbiAgICAgICAgPC9oMj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtbWQgZm9udC1tZWRpdW0gdGV4dC1icmFuZC1ibHVlLTMwMFwiPlByb2dyZXNzOiA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LW9yYW5nZS02MDBcIj57YXBwcm92ZWRDb3VudH08L3NwYW4+IG9mIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtYnJhbmQtYmx1ZS0zMDBcIj57dG90YWxDb3VudH08L3NwYW4+IGFwcHJvdmVkPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBiZy1icmFuZC1ibHVlLTMwMC9bMjAlXSByb3VuZGVkLWZ1bGwgaC0yLjUgbWItNlwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JlZW4tNTAwIGgtMi41IHJvdW5kZWQtZnVsbCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi01MDAgZWFzZS1vdXRcIiBzdHlsZT17eyB3aWR0aDogYCR7cHJvZ3Jlc3NQZXJjZW50YWdlfSVgIH19PjwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuXHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMyBnYXAtNFwiPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbC1zcGFuLTJcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBmbGV4IGZsZXgtY29sXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC00IG1iLTRcIj5cclxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtYm9sZCB0ZXh0LWJyYW5kLWJsdWUtMzAwXCI+e3BhZ2UudGl0bGV9PC9oMz5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgZmxleCBqdXN0aWZ5LWVuZCBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgIDxUb29sdGlwPlxyXG4gICAgICAgICAgICAgICAgPFRvb2x0aXBUcmlnZ2VyIGFzQ2hpbGQ+XHJcbiAgICAgICAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cIm91dGxpbmVcIiBjbGFzc05hbWU9XCJweC00IHB5LTIgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHRleHQtZ3JheS03MDAgaG92ZXI6YmctZ3JheS0xMDAgdGV4dC1zbSBmb250LW1lZGl1bSB0cmFuc2l0aW9uIGZsZXggaXRlbXMtY2VudGVyIGdhcC0xIHNoYWRvdy1zbVwiIG9uQ2xpY2s9e29uUmVnZW5lcmF0ZX0+XHJcbiAgICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtZ3JheS02MDBcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHN0cm9rZVdpZHRoPVwiMlwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj48cGF0aCBkPVwiTTQgNHY1aC41ODJNMjAgMjB2LTVoLS41ODFcIi8+PHBhdGggZD1cIk01LjA3NyAxOUE5IDkgMCAxIDAgNi41IDYuNUw0IDkuNVwiLz48L3N2Zz5cclxuICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICA8L1Rvb2x0aXBUcmlnZ2VyPlxyXG4gICAgICAgICAgICAgICAgPFRvb2x0aXBDb250ZW50IHZhcmlhbnQ9XCJicmFuZFwiPlxyXG4gICAgICAgICAgICAgICAgICA8cD5SZWdlbmVyYXRlPC9wPlxyXG4gICAgICAgICAgICAgICAgPC9Ub29sdGlwQ29udGVudD5cclxuICAgICAgICAgICAgICA8L1Rvb2x0aXA+XHJcbiAgICAgICAgICAgICAgPFRvb2x0aXA+XHJcbiAgICAgICAgICAgICAgICA8VG9vbHRpcFRyaWdnZXIgYXNDaGlsZD5cclxuICAgICAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwib3V0bGluZVwiIGNsYXNzTmFtZT1cInB4LTQgcHktMiByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItZ3JheS0zMDAgdGV4dC1ncmF5LTcwMCBob3ZlcjpiZy1ncmF5LTEwMCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24gZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTEgc2hhZG93LXNtXCIgb25DbGljaz17b25DdXN0b21Qcm9tcHR9PlxyXG4gICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtZ3JheS02MDBcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHN0cm9rZVdpZHRoPVwiMlwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj48cGF0aCBkPVwiTTE2Ljg2MiAzLjQ4N2EyLjYgMi42IDAgMSAxIDMuNjc3IDMuNjc3TDcuNSAyMC4yMDVsLTQuNSAxIDEtNC41TDE2Ljg2MiAzLjQ4N1pcIi8+PHBhdGggZD1cIk0xNSA2LjUgMTcuNSA5XCIvPjwvc3ZnPlxyXG4gICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgIDwvVG9vbHRpcFRyaWdnZXI+XHJcbiAgICAgICAgICAgICAgICA8VG9vbHRpcENvbnRlbnQgdmFyaWFudD1cImJyYW5kXCI+XHJcbiAgICAgICAgICAgICAgICAgICA8cD5DdXN0b20gUHJvbXB0PC9wPlxyXG4gICAgICAgICAgICAgICAgPC9Ub29sdGlwQ29udGVudD5cclxuICAgICAgICAgICAgICA8L1Rvb2x0aXA+XHJcbiAgICAgICAgICAgICAgPFRvb2x0aXA+XHJcbiAgICAgICAgICAgICAgICA8VG9vbHRpcFRyaWdnZXIgYXNDaGlsZD5cclxuICAgICAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwib3V0bGluZVwiIGNsYXNzTmFtZT1cInB4LTQgcHktMiByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItZ3JheS0zMDAgdGV4dC1ncmF5LTcwMCBob3ZlcjpiZy1ncmF5LTEwMCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24gZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTEgc2hhZG93LXNtXCIgb25DbGljaz17b25WaWV3T3JpZ2luYWx9PlxyXG4gICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtZ3JheS02MDBcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHN0cm9rZVdpZHRoPVwiMlwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj48cGF0aCBkPVwiTTEuNSAxMlM1IDUgMTIgNXMxMC41IDcgMTAuNSA3LTMuNSA3LTEwLjUgN1MxLjUgMTIgMS41IDEyWlwiLz48Y2lyY2xlIGN4PVwiMTJcIiBjeT1cIjEyXCIgcj1cIjNcIi8+PC9zdmc+XHJcbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgIDwvVG9vbHRpcFRyaWdnZXI+XHJcbiAgICAgICAgICAgICAgICA8VG9vbHRpcENvbnRlbnQgdmFyaWFudD1cImJyYW5kXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxwPlZpZXcgT3JpZ2luYWw8L3A+XHJcbiAgICAgICAgICAgICAgICAgIDwvVG9vbHRpcENvbnRlbnQ+XHJcbiAgICAgICAgICAgICAgPC9Ub29sdGlwPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02IGZsZXgtMSBmbGV4IGZsZXgtY29sXCI+XHJcbiAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMlwiPkFJLUdlbmVyYXRlZCBTdW1tYXJ5PC9sYWJlbD5cclxuICAgICAgICAgICAgPHRleHRhcmVhXHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJvcmRlciBib3JkZXItZ3JheS0yMDAgcm91bmRlZC1sZyBweC00IHB5LTMgbWluLWgtWzE1MHB4XSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1vcmFuZ2UtMjAwIGZvY3VzOmJvcmRlci1vcmFuZ2UtMzAwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMCByZXNpemUteSBmbGV4LTFcIlxyXG4gICAgICAgICAgICAgIHZhbHVlPXtwYWdlLmFpU3VtbWFyeX1cclxuICAgICAgICAgICAgICBvbkNoYW5nZT17ZSA9PiBvbkNoYW5nZVN1bW1hcnkoZS50YXJnZXQudmFsdWUpfVxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIG10LTQgdGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XHJcbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYmctb3JhbmdlLTUwIHRleHQtb3JhbmdlLTYwMCBweC0yIHB5LTEgcm91bmRlZC1mdWxsIGZvbnQtbWVkaXVtXCI+R2VuZXJhdGVkIGJ5IEF6dXJlIE9wZW5BSSBHUFQtNDwvc3Bhbj5cclxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJiZy1icmFuZC1ibHVlL1syNSVdIHRleHQtYnJhbmQtYmx1ZSBweC0yIHB5LTEgcm91bmRlZC1mdWxsIGZvbnQtbWVkaXVtXCI+TWVkaWNhbCBBSSBNb2RlbDwvc3Bhbj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNiBmbGV4LTEgZmxleCBmbGV4LWNvbFwiPlxyXG4gICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj5PcmlnaW5hbCBDb250ZW50IFByZXZpZXc8L2xhYmVsPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktNTAgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCByb3VuZGVkLWxnIHB4LTQgcHktMyB0ZXh0LWdyYXktNzAwIHRleHQtc20gbWluLWgtWzE1MHB4XSBvdmVyZmxvdy15LWF1dG8gZmxleC0xIGN1c3RvbS1zY3JvbGxiYXJcIj5cclxuICAgICAgICAgICAgICB7cGFnZS5vcmlnaW5hbH1cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMyBtdC0yIGp1c3RpZnktc3RhcnRcIj5cclxuICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PXsnb3V0bGluZSd9IGNsYXNzTmFtZT1cInRleHQtYnJhbmQtYmx1ZSBib3JkZXItYnJhbmQtYmx1ZSBob3ZlcjpiZy1icmFuZC1ibHVlIGhvdmVyOnRleHQtd2hpdGUgcHgtNiBweS0zIHJvdW5kZWQtbGcgZm9udC1zZW1pYm9sZCBzaGFkb3ctbWQgaG92ZXI6c2hhZG93LWxnIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiIG9uQ2xpY2s9e29uQXBwcm92ZX0+XHJcbiAgICAgICAgICAgICAgPENoZWNrIGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPlxyXG4gICAgICAgICAgICAgIEFwcHJvdmVcclxuICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD17J291dGxpbmUnfSBjbGFzc05hbWU9XCJ0ZXh0LWJyYW5kLWJsdWUgYm9yZGVyLWJyYW5kLWJsdWUgaG92ZXI6YmctYnJhbmQtYmx1ZSBob3Zlcjp0ZXh0LXdoaXRlIHB4LTYgcHktMyByb3VuZGVkLWxnIGZvbnQtc2VtaWJvbGQgc2hhZG93LW1kIGhvdmVyOnNoYWRvdy1sZyB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIiBvbkNsaWNrPXtvbkFwcHJvdmVBbGx9PlxyXG4gICAgICAgICAgICAgIDxDaGVja0NoZWNrIGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPlxyXG4gICAgICAgICAgICAgIEFwcHJvdmUgQWxsICh7cGFnZXMuZmlsdGVyKChwOiBhbnkpID0+IHAuc3RhdHVzID09PSBcIlBlbmRpbmcgUmV2aWV3XCIpLmxlbmd0aH0gcmVtYWluaW5nKVxyXG4gICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PiAgICAgIFxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIlwiPlxyXG4gICAgICAgIDxQYWdlTGlzdCBwYWdlcz17cGFnZXN9IHNlbGVjdGVkPXtzZWxlY3RlZH0gb25TZWxlY3Q9e29uU2VsZWN0fSAvPlxyXG4gICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICB7LyogPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC04IGZsZXgtMVwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04MCBmbGV4LXNocmluay0wXCI+XHJcbiAgICAgICAgICA8UGFnZUxpc3QgcGFnZXM9e3BhZ2VzfSBzZWxlY3RlZD17c2VsZWN0ZWR9IG9uU2VsZWN0PXtvblNlbGVjdH0gLz5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgICBcclxuICAgICAgPC9kaXY+ICovfVxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQWlTdW1tYXJpemF0aW9uUGFuZWwoKSB7XHJcbiAgY29uc3QgW3NlbGVjdGVkLCBzZXRTZWxlY3RlZF0gPSB1c2VTdGF0ZSgwKTtcclxuICBjb25zdCBbcGFnZXMsIHNldFBhZ2VzXSA9IHVzZVN0YXRlPGFueVtdPihbXSk7XHJcbiAgY29uc3QgW3Nob3dQcm9tcHRNb2RhbCwgc2V0U2hvd1Byb21wdE1vZGFsXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbY3VzdG9tUHJvbXB0LCBzZXRDdXN0b21Qcm9tcHRdID0gdXNlU3RhdGUoXCJcIik7XHJcbiAgY29uc3QgW2NvdXJzZXMsIHNldENvdXJzZXNdID0gdXNlU3RhdGU8YW55W10+KFtdKTtcclxuICBjb25zdCBbc2VsZWN0ZWRDb3Vyc2VJZCwgc2V0U2VsZWN0ZWRDb3Vyc2VJZF0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihcIlwiKTtcclxuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW3Nob3dBcHByb3ZlQWxsTW9kYWwsIHNldFNob3dBcHByb3ZlQWxsTW9kYWxdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtzdWNjZXNzTWVzc2FnZSwgc2V0U3VjY2Vzc01lc3NhZ2VdID0gdXNlU3RhdGUoXCJcIik7XHJcbiAgY29uc3QgW3NlbGVjdGVkQ291cnNlQmxvYlVybCwgc2V0U2VsZWN0ZWRDb3Vyc2VCbG9iVXJsXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgLy8gRmV0Y2ggY291cnNlcyBvbiBtb3VudFxyXG4gICAgZmV0Y2gocHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfRU5EX1BPSU5UK1wiL2FwaS9jb3Vyc2VzXCIpXHJcbiAgICAgIC50aGVuKHJlcyA9PiByZXMuanNvbigpKVxyXG4gICAgICAudGhlbihkYXRhID0+IHNldENvdXJzZXMoZGF0YSkpO1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgY29uc3QgaGFuZGxlQ291cnNlU2VsZWN0ID0gYXN5bmMgKHZhbHVlOnN0cmluZykgPT4ge1xyXG4gICAgY29uc3QgY291cnNlSWQgPSB2YWx1ZTtcclxuICAgIHNldFNlbGVjdGVkQ291cnNlSWQoY291cnNlSWQpO1xyXG4gICAgc2V0TG9hZGluZyh0cnVlKTtcclxuICAgIGNvbnN0IHJlcyA9IGF3YWl0IGZldGNoKHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0VORF9QT0lOVCsnL2FwaS9jb3Vyc2VzLycrY291cnNlSWQpO1xyXG4gICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlcy5qc29uKCk7XHJcbiAgICAvLyBNYXAgcGRmX291dHB1dCB0byB0aGUgZm9ybWF0IGV4cGVjdGVkIGJ5IHRoZSBVSVxyXG4gICAgY29uc3QgbWFwcGVkUGFnZXMgPSAoZGF0YS5wZGZfb3V0cHV0IHx8IFtdKS5tYXAoKHA6IGFueSwgaWR4OiBudW1iZXIpID0+ICh7XHJcbiAgICAgIHBhZ2U6IHAucGFnZSB8fCBpZHggKyAxLFxyXG4gICAgICB0aXRsZTogYFBhZ2UgJHtwLnBhZ2UgfHwgaWR4ICsgMX1gLFxyXG4gICAgICBzdGF0dXM6IFwiUGVuZGluZyBSZXZpZXdcIixcclxuICAgICAgYWlTdW1tYXJ5OiBwLmltYWdldGV4dCB8fCBcIlwiLFxyXG4gICAgICBvcmlnaW5hbDogcC5pbWFnZXRleHQgfHwgXCJcIixcclxuICAgICAgaW1hZ2Vhc3Nlc3RpZDogcC5pbWFnZWFzc2VzdGlkXHJcbiAgICB9KSk7XHJcbiAgICBzZXRQYWdlcyhtYXBwZWRQYWdlcyk7XHJcbiAgICBzZXRTZWxlY3RlZCgwKTtcclxuICAgIHNldExvYWRpbmcoZmFsc2UpO1xyXG4gICAgc2V0U2VsZWN0ZWRDb3Vyc2VCbG9iVXJsKGRhdGEuYmxvYl91cmwgfHwgbnVsbCk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlQ2hhbmdlU3VtbWFyeSA9ICh2YWw6IHN0cmluZykgPT4ge1xyXG4gICAgc2V0UGFnZXMocGFnZXMgPT4gcGFnZXMubWFwKChwLCBpZHgpID0+IGlkeCA9PT0gc2VsZWN0ZWQgPyB7IC4uLnAsIGFpU3VtbWFyeTogdmFsIH0gOiBwKSk7XHJcbiAgfTtcclxuICBjb25zdCBoYW5kbGVBcHByb3ZlID0gKCkgPT4ge1xyXG4gICAgc2V0UGFnZXMocGFnZXMgPT4gcGFnZXMubWFwKChwLCBpZHgpID0+IGlkeCA9PT0gc2VsZWN0ZWQgPyB7IC4uLnAsIHN0YXR1czogXCJBcHByb3ZlZFwiIH0gOiBwKSk7XHJcbiAgfTtcclxuICBjb25zdCBoYW5kbGVBcHByb3ZlQWxsID0gKCkgPT4ge1xyXG4gICAgc2V0UGFnZXMocGFnZXMgPT4gcGFnZXMubWFwKHAgPT4gKHsgLi4ucCwgc3RhdHVzOiBcIkFwcHJvdmVkXCIgfSkpKTtcclxuICB9O1xyXG4gIGNvbnN0IGhhbmRsZVJlZ2VuZXJhdGUgPSBhc3luYyAoKSA9PiB7XHJcbiAgICBjb25zdCB0ZXh0ID0gcGFnZXNbc2VsZWN0ZWRdLmFpU3VtbWFyeTtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGZldGNoKGAke3Byb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0VORF9QT0lOVH0vYXBpL3JlcGhyYXNlYCwge1xyXG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXHJcbiAgICAgICAgfSxcclxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7IHRleHQgfSksXHJcbiAgICAgIH0pO1xyXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzLmpzb24oKTtcclxuICAgICAgaWYgKGRhdGEgJiYgZGF0YS5yZXBocmFzZWQpIHtcclxuICAgICAgICBzZXRQYWdlcyhwYWdlcyA9PiBwYWdlcy5tYXAoKHAsIGlkeCkgPT4gaWR4ID09PSBzZWxlY3RlZCA/IHsgLi4ucCwgYWlTdW1tYXJ5OiBkYXRhLnJlcGhyYXNlZCB9IDogcCkpO1xyXG4gICAgICB9IGVsc2UgaWYgKHR5cGVvZiBkYXRhID09PSAnc3RyaW5nJykge1xyXG4gICAgICAgIHNldFBhZ2VzKHBhZ2VzID0+IHBhZ2VzLm1hcCgocCwgaWR4KSA9PiBpZHggPT09IHNlbGVjdGVkID8geyAuLi5wLCBhaVN1bW1hcnk6IGRhdGEgfSA6IHApKTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyKSB7XHJcbiAgICAgIGFsZXJ0KCdGYWlsZWQgdG8gcmVwaHJhc2UuJyk7XHJcbiAgICB9XHJcbiAgfTtcclxuICBjb25zdCBoYW5kbGVDdXN0b21Qcm9tcHQgPSAoKSA9PiB7XHJcbiAgICBzZXRTaG93UHJvbXB0TW9kYWwodHJ1ZSk7XHJcbiAgfTtcclxuICBjb25zdCBoYW5kbGVWaWV3T3JpZ2luYWwgPSAoKSA9PiB7XHJcbiAgICBpZiAoc2VsZWN0ZWRDb3Vyc2VCbG9iVXJsKSB7XHJcbiAgICAgIHdpbmRvdy5vcGVuKHNlbGVjdGVkQ291cnNlQmxvYlVybCwgJ19ibGFuaycpO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgYWxlcnQoXCJObyBvcmlnaW5hbCBjb250ZW50IFVSTCBhdmFpbGFibGUuXCIpO1xyXG4gICAgfVxyXG4gIH07XHJcbiAgY29uc3QgaGFuZGxlUHJvbXB0Q2FuY2VsID0gKCkgPT4ge1xyXG4gICAgc2V0U2hvd1Byb21wdE1vZGFsKGZhbHNlKTtcclxuICAgIHNldEN1c3RvbVByb21wdChcIlwiKTtcclxuICB9O1xyXG4gIGNvbnN0IGhhbmRsZVByb21wdEdlbmVyYXRlID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgY29uc3Qgc3VtbWFyeVRleHQgPSBwYWdlc1tzZWxlY3RlZF0uYWlTdW1tYXJ5O1xyXG4gICAgY29uc3QgcHJvbXB0VGV4dCA9IGN1c3RvbVByb21wdDtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGZldGNoKGAke3Byb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0VORF9QT0lOVF9SRVBIQVJTRX0vYXBpL2N1c3RvbXJlcGhyYXNlYCwge1xyXG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXHJcbiAgICAgICAgfSxcclxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7IHRleHQ6IHN1bW1hcnlUZXh0LCBwcm9tcHQ6IHByb21wdFRleHQgfSksXHJcbiAgICAgIH0pO1xyXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzLmpzb24oKTtcclxuICAgICAgaWYgKGRhdGEgJiYgZGF0YS5yZXN1bHQpIHtcclxuICAgICAgICBzZXRQYWdlcyhwYWdlcyA9PiBwYWdlcy5tYXAoKHAsIGlkeCkgPT4gaWR4ID09PSBzZWxlY3RlZCA/IHsgLi4ucCwgYWlTdW1tYXJ5OiBkYXRhLnJlc3VsdCB9IDogcCkpO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIGFsZXJ0KCdGYWlsZWQgdG8gZ2V0IHJlcGhyYXNlZCByZXN1bHQuJyk7XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycikge1xyXG4gICAgICBhbGVydCgnRmFpbGVkIHRvIGdldCByZXBocmFzZWQgcmVzdWx0LicpO1xyXG4gICAgfVxyXG4gICAgc2V0U2hvd1Byb21wdE1vZGFsKGZhbHNlKTtcclxuICAgIHNldEN1c3RvbVByb21wdChcIlwiKTtcclxuICB9O1xyXG4gIGNvbnN0IGhhbmRsZUFwcHJvdmVBbGxDbGljayA9ICgpID0+IHtcclxuICAgIHNldFNob3dBcHByb3ZlQWxsTW9kYWwodHJ1ZSk7XHJcbiAgfTtcclxuICBjb25zdCBoYW5kbGVBcHByb3ZlQWxsQ29uZmlybSA9IGFzeW5jICgpID0+IHtcclxuICAgIHNldFNob3dBcHByb3ZlQWxsTW9kYWwoZmFsc2UpO1xyXG4gICAgaGFuZGxlQXBwcm92ZUFsbCgpO1xyXG5cclxuICAgIC8vIEdldCB1c2VyIGVtYWlsIGZyb20gbG9jYWxTdG9yYWdlIHRva2VuXHJcbiAgICBsZXQgdXNlckVtYWlsID0gXCJ1bmtub3duXCI7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKFwidG9rZW5cIik7XHJcbiAgICAgIGlmICh0b2tlbikge1xyXG4gICAgICAgIGNvbnN0IHVzZXJTdHIgPSBhdG9iKHRva2VuLnNwbGl0KFwiLlwiKVsxXSk7XHJcbiAgICAgICAgY29uc3QgdXNlciA9IEpTT04ucGFyc2UodXNlclN0cik7XHJcbiAgICAgICAgdXNlckVtYWlsID0gdXNlci5lbWFpbCB8fCBcInVua25vd25cIjtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCB7fVxyXG5cclxuICAgIC8vIFNlbmQgYXBwcm92ZWQgZGF0YSB0byBiYWNrZW5kXHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCByZXMgPSBhd2FpdCBmZXRjaChgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19FTkRfUE9JTlR9L2FwaS9jb3Vyc2VzLyR7c2VsZWN0ZWRDb3Vyc2VJZH0vYXBwcm92ZWAsIHtcclxuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcclxuICAgICAgICBoZWFkZXJzOiB7ICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicgfSxcclxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XHJcbiAgICAgICAgICBjb250ZW50OiBwYWdlcy5tYXAocCA9PiAoeyAuLi5wLCBzdGF0dXM6IFwiQXBwcm92ZWRcIiB9KSksXHJcbiAgICAgICAgICBhcHByb3ZlZF9ieTogdXNlckVtYWlsLFxyXG4gICAgICAgICAgY29tbWVudDogJydcclxuICAgICAgICB9KVxyXG4gICAgICB9KTtcclxuICAgICAgaWYgKCFyZXMub2spIHtcclxuICAgICAgICBhbGVydCgnRmFpbGVkIHRvIHNhdmUgYXBwcm92ZWQgY29udGVudCEnKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBzZXRTdWNjZXNzTWVzc2FnZSgnQ29udGVudCBhcHByb3ZlZCBhbmQgc2F2ZWQhJyk7XHJcbiAgICAgICAgc2V0VGltZW91dCgoKSA9PiBzZXRTdWNjZXNzTWVzc2FnZShcIlwiKSwgMzAwMCk7XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycikge1xyXG4gICAgICBhbGVydCgnRmFpbGVkIHRvIHNhdmUgYXBwcm92ZWQgY29udGVudCEnKTtcclxuICAgIH1cclxuICB9O1xyXG4gIGNvbnN0IGhhbmRsZUFwcHJvdmVBbGxDYW5jZWwgPSAoKSA9PiB7XHJcbiAgICBzZXRTaG93QXBwcm92ZUFsbE1vZGFsKGZhbHNlKTtcclxuICB9O1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtNCBweS00IGZsZXggZmxleC1jb2wgaXRlbXMtc3RyZXRjaCBqdXN0aWZ5LXN0YXJ0IHJlbGF0aXZlXCI+XHJcbiAgICAgIHsvKiBDb3Vyc2Ugc2VsZWN0aW9uIGZvcm0gKi99XHJcbiAgICAgIHsvKiA8ZGl2IGNsYXNzTmFtZT1cInctYXV0byBwLTggYmctd2hpdGUgcm91bmRlZC0yeGwgc2hhZG93LWxnIHAtOCBtdC04IG1iLThcIj4gKi99XHJcbiAgICAgICAgPGZvcm0gY2xhc3NOYW1lPVwiZmxleCBnYXAtNCBpdGVtcy1lbmQgcHktNFwiIG9uU3VibWl0PXtlID0+IGUucHJldmVudERlZmF1bHQoKX0+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxyXG4gICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImNvdXJzZVwiIGNsYXNzTmFtZT1cInB5LTJcIj5Db3Vyc2U8L0xhYmVsPlxyXG4gICAgICAgICAgICA8U2VsZWN0XHJcbiAgICAgICAgICAgICAgICB2YWx1ZT17c2VsZWN0ZWRDb3Vyc2VJZCB8fCAnJ31cclxuICAgICAgICAgICAgICAgIG9uVmFsdWVDaGFuZ2U9eyh2YWx1ZSkgPT4gaGFuZGxlQ291cnNlU2VsZWN0KHZhbHVlKX1cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICA8U2VsZWN0VHJpZ2dlciBjbGFzc05hbWU9XCJ3LWF1dG8gZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctb3JhbmdlLTIwMFwiPlxyXG4gICAgICAgICAgICAgICAgICA8U2VsZWN0VmFsdWUgcGxhY2Vob2xkZXI9XCJTZWxlY3QgQ291cnNlXCIgLz5cclxuICAgICAgICAgICAgICAgIDwvU2VsZWN0VHJpZ2dlcj5cclxuICAgICAgICAgICAgICAgIDxTZWxlY3RDb250ZW50PlxyXG4gICAgICAgICAgICAgICAgICB7Y291cnNlcy5tYXAoKGM6IGFueSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgLy8gPG9wdGlvbiBrZXk9e2MuaWR9IHZhbHVlPXtjLmlkfT57Yy50aXRsZX0gKHtjLmRvbWFpbn0pPC9vcHRpb24+XHJcbiAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIGtleT17Yy5pZH0gdmFsdWU9e2MuaWQudG9TdHJpbmcoKX0+e2MudGl0bGV9ICh7Yy5kb21haW59KTwvU2VsZWN0SXRlbT5cclxuICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgICAgIDwvU2VsZWN0Q29udGVudD5cclxuICAgICAgICAgICAgICA8L1NlbGVjdD5cclxuICAgICAgICAgICAgey8qIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayBtYi0xIGZvbnQtbWVkaXVtXCI+U2VsZWN0IENvdXJzZTwvbGFiZWw+XHJcbiAgICAgICAgICAgIDxzZWxlY3RcclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCByb3VuZGVkIHB4LTMgcHktMlwiXHJcbiAgICAgICAgICAgICAgdmFsdWU9e3NlbGVjdGVkQ291cnNlSWQgfHwgJyd9XHJcbiAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUNvdXJzZVNlbGVjdH1cclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJcIj4tLSBDaG9vc2UgYSBjb3Vyc2UgLS08L29wdGlvbj5cclxuICAgICAgICAgICAgICB7Y291cnNlcy5tYXAoKGM6IGFueSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgPG9wdGlvbiBrZXk9e2MuaWR9IHZhbHVlPXtjLmlkfT57Yy50aXRsZX0gKHtjLmRvbWFpbn0pPC9vcHRpb24+XHJcbiAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgIDwvc2VsZWN0PiAqL31cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZm9ybT5cclxuICAgICAgey8qIDwvZGl2PiAqL31cclxuICAgICAge2xvYWRpbmcgJiYgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciB0ZXh0LWxnIHRleHQtb3JhbmdlLTUwMFwiPkxvYWRpbmcgY291cnNlIGRhdGEuLi48L2Rpdj59XHJcbiAgICAgIHshbG9hZGluZyAmJiBwYWdlcy5sZW5ndGggPiAwICYmIChcclxuICAgICAgICAvLyA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBiZy13aGl0ZSByb3VuZGVkLTJ4bCBzaGFkb3ctbGcgcC04IG10LThcIj5cclxuICAgICAgICAgIDxTdW1tYXJ5UGFuZWxcclxuICAgICAgICAgICAgcGFnZT17cGFnZXNbc2VsZWN0ZWRdfVxyXG4gICAgICAgICAgICBvbkNoYW5nZVN1bW1hcnk9e2hhbmRsZUNoYW5nZVN1bW1hcnl9XHJcbiAgICAgICAgICAgIG9uQXBwcm92ZT17aGFuZGxlQXBwcm92ZX1cclxuICAgICAgICAgICAgb25SZWdlbmVyYXRlPXtoYW5kbGVSZWdlbmVyYXRlfVxyXG4gICAgICAgICAgICBvbkN1c3RvbVByb21wdD17aGFuZGxlQ3VzdG9tUHJvbXB0fVxyXG4gICAgICAgICAgICBvblZpZXdPcmlnaW5hbD17aGFuZGxlVmlld09yaWdpbmFsfVxyXG4gICAgICAgICAgICBvbkFwcHJvdmVBbGw9e2hhbmRsZUFwcHJvdmVBbGxDbGlja31cclxuICAgICAgICAgICAgYXBwcm92ZWRDb3VudD17cGFnZXMuZmlsdGVyKChwOiBhbnkpID0+IHAuc3RhdHVzID09PSBcIkFwcHJvdmVkXCIpLmxlbmd0aH1cclxuICAgICAgICAgICAgdG90YWxDb3VudD17cGFnZXMubGVuZ3RofVxyXG4gICAgICAgICAgICBwYWdlcz17cGFnZXN9XHJcbiAgICAgICAgICAgIHNlbGVjdGVkPXtzZWxlY3RlZH1cclxuICAgICAgICAgICAgb25TZWxlY3Q9e3NldFNlbGVjdGVkfVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICAvLyA8L2Rpdj5cclxuICAgICAgKX1cclxuXHJcbiAgICAgIHsvKiBDdXN0b20gUHJvbXB0IE1vZGFsICovfVxyXG4gICAgICB7c2hvd1Byb21wdE1vZGFsICYmIChcclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgYmctYmxhY2sgYmctb3BhY2l0eS01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB6LTUwIHAtNFwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLTJ4bCBzaGFkb3ctMnhsIHAtOCBtYXgtdy1tZCB3LWZ1bGwgYW5pbWF0ZS1mYWRlLWluLXVwIHJlbGF0aXZlXCI+XHJcbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktODAwIG1iLTRcIj5DdXN0b20gQUkgUHJvbXB0PC9oMz5cclxuICAgICAgICAgICAgPHRleHRhcmVhXHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1sZyBwLTMgbWluLWgtWzEyMHB4XSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1vcmFuZ2UtMjAwIGZvY3VzOmJvcmRlci1vcmFuZ2UtMzAwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMCByZXNpemUteVwiXHJcbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciB5b3VyIGN1c3RvbSBwcm9tcHQgaGVyZSB0byByZWdlbmVyYXRlIHRoZSBzdW1tYXJ5Li4uXCJcclxuICAgICAgICAgICAgICB2YWx1ZT17Y3VzdG9tUHJvbXB0fVxyXG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Q3VzdG9tUHJvbXB0KGUudGFyZ2V0LnZhbHVlKX1cclxuICAgICAgICAgICAgPjwvdGV4dGFyZWE+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWVuZCBnYXAtMyBtdC02XCI+XHJcbiAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctZ3JheS0yMDAgaG92ZXI6YmctZ3JheS0zMDAgdGV4dC1ncmF5LTcwMCBweC02IHB5LTMgcm91bmRlZC1sZyBmb250LXNlbWlib2xkIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMFwiXHJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVQcm9tcHRDYW5jZWx9XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgQ2FuY2VsXHJcbiAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctb3JhbmdlLTUwMCBob3ZlcjpiZy1vcmFuZ2UtNjAwIHRleHQtd2hpdGUgcHgtNiBweS0zIHJvdW5kZWQtbGcgZm9udC1zZW1pYm9sZCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIlxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlUHJvbXB0R2VuZXJhdGV9XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgR2VuZXJhdGVcclxuICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgKX1cclxuXHJcbiAgICAgIHsvKiBBcHByb3ZlIEFsbCBDb25maXJtYXRpb24gTW9kYWwgKi99XHJcbiAgICAgIHtzaG93QXBwcm92ZUFsbE1vZGFsICYmIChcclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgYmctYmxhY2sgYmctb3BhY2l0eS01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB6LTUwIHAtNFwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLTJ4bCBzaGFkb3ctMnhsIHAtOCBtYXgtdy1zbSB3LWZ1bGwgYW5pbWF0ZS1mYWRlLWluLXVwIHJlbGF0aXZlIHRleHQtY2VudGVyXCI+XHJcbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktODAwIG1iLTRcIj5Db25maXJtIEFwcHJvdmFsPC9oMz5cclxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBtYi02XCI+QXJlIHlvdSBzdXJlIHlvdSB3YW50IHRvIGFwcHJvdmUgYWxsIHBlbmRpbmcgc3VtbWFyaWVzIGZvciB0aGlzIGNvdXJzZT8gVGhpcyBhY3Rpb24gY2Fubm90IGJlIHVuZG9uZS48L3A+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlciBnYXAtNFwiPlxyXG4gICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyYXktMjAwIGhvdmVyOmJnLWdyYXktMzAwIHRleHQtZ3JheS03MDAgcHgtNiBweS0zIHJvdW5kZWQtbGcgZm9udC1zZW1pYm9sZCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIlxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQXBwcm92ZUFsbENhbmNlbH1cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICBDYW5jZWxcclxuICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ncmVlbi01MDAgaG92ZXI6YmctZ3JlZW4tNjAwIHRleHQtd2hpdGUgcHgtNiBweS0zIHJvdW5kZWQtbGcgZm9udC1zZW1pYm9sZCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIlxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQXBwcm92ZUFsbENvbmZpcm19XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgQ29uZmlybSBBcHByb3ZlIEFsbFxyXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICApfVxyXG5cclxuICAgICAge3N1Y2Nlc3NNZXNzYWdlICYmIChcclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGJvdHRvbS02IHJpZ2h0LTYgYmctZ3JlZW4tNTAwIHRleHQtd2hpdGUgcHgtNiBweS0zIHJvdW5kZWQtbGcgc2hhZG93LXhsIHRleHQtbGcgZm9udC1zZW1pYm9sZCBhbmltYXRlLWZhZGUtaW4tcmlnaHQgei01MFwiPlxyXG4gICAgICAgICAge3N1Y2Nlc3NNZXNzYWdlfVxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICApfVxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufSJdLCJuYW1lcyI6WyJCdXR0b24iLCJMYWJlbCIsIlNlbGVjdCIsIlNlbGVjdENvbnRlbnQiLCJTZWxlY3RJdGVtIiwiU2VsZWN0VHJpZ2dlciIsIlNlbGVjdFZhbHVlIiwiVG9vbHRpcCIsIlRvb2x0aXBDb250ZW50IiwiVG9vbHRpcFRyaWdnZXIiLCJDaGVjayIsIkNoZWNrQ2hlY2siLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIlBhZ2VMaXN0IiwicGFnZXMiLCJzZWxlY3RlZCIsIm9uU2VsZWN0IiwiZGl2IiwiY2xhc3NOYW1lIiwiaDMiLCJsZW5ndGgiLCJ1bCIsIm1hcCIsInAiLCJpZHgiLCJsaSIsImJ1dHRvbiIsIm9uQ2xpY2siLCJwYWdlIiwidGl0bGUiLCJzcGFuIiwic3RhdHVzIiwiU3VtbWFyeVBhbmVsIiwib25DaGFuZ2VTdW1tYXJ5Iiwib25BcHByb3ZlIiwib25SZWdlbmVyYXRlIiwib25DdXN0b21Qcm9tcHQiLCJvblZpZXdPcmlnaW5hbCIsIm9uQXBwcm92ZUFsbCIsImFwcHJvdmVkQ291bnQiLCJ0b3RhbENvdW50IiwicHJvZ3Jlc3NQZXJjZW50YWdlIiwiaDIiLCJzdmciLCJmaWxsIiwic3Ryb2tlIiwidmlld0JveCIsInBhdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJzdHJva2VXaWR0aCIsImQiLCJzdHlsZSIsIndpZHRoIiwiYXNDaGlsZCIsInZhcmlhbnQiLCJjaXJjbGUiLCJjeCIsImN5IiwiciIsImxhYmVsIiwidGV4dGFyZWEiLCJ2YWx1ZSIsImFpU3VtbWFyeSIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsIm9yaWdpbmFsIiwiZmlsdGVyIiwiQWlTdW1tYXJpemF0aW9uUGFuZWwiLCJzZXRTZWxlY3RlZCIsInNldFBhZ2VzIiwic2hvd1Byb21wdE1vZGFsIiwic2V0U2hvd1Byb21wdE1vZGFsIiwiY3VzdG9tUHJvbXB0Iiwic2V0Q3VzdG9tUHJvbXB0IiwiY291cnNlcyIsInNldENvdXJzZXMiLCJzZWxlY3RlZENvdXJzZUlkIiwic2V0U2VsZWN0ZWRDb3Vyc2VJZCIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwic2hvd0FwcHJvdmVBbGxNb2RhbCIsInNldFNob3dBcHByb3ZlQWxsTW9kYWwiLCJzdWNjZXNzTWVzc2FnZSIsInNldFN1Y2Nlc3NNZXNzYWdlIiwic2VsZWN0ZWRDb3Vyc2VCbG9iVXJsIiwic2V0U2VsZWN0ZWRDb3Vyc2VCbG9iVXJsIiwiZmV0Y2giLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfRU5EX1BPSU5UIiwidGhlbiIsInJlcyIsImpzb24iLCJkYXRhIiwiaGFuZGxlQ291cnNlU2VsZWN0IiwiY291cnNlSWQiLCJtYXBwZWRQYWdlcyIsInBkZl9vdXRwdXQiLCJpbWFnZXRleHQiLCJpbWFnZWFzc2VzdGlkIiwiYmxvYl91cmwiLCJoYW5kbGVDaGFuZ2VTdW1tYXJ5IiwidmFsIiwiaGFuZGxlQXBwcm92ZSIsImhhbmRsZUFwcHJvdmVBbGwiLCJoYW5kbGVSZWdlbmVyYXRlIiwidGV4dCIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsInJlcGhyYXNlZCIsImVyciIsImFsZXJ0IiwiaGFuZGxlQ3VzdG9tUHJvbXB0IiwiaGFuZGxlVmlld09yaWdpbmFsIiwid2luZG93Iiwib3BlbiIsImhhbmRsZVByb21wdENhbmNlbCIsImhhbmRsZVByb21wdEdlbmVyYXRlIiwic3VtbWFyeVRleHQiLCJwcm9tcHRUZXh0IiwiTkVYVF9QVUJMSUNfRU5EX1BPSU5UX1JFUEhBUlNFIiwicHJvbXB0IiwicmVzdWx0IiwiaGFuZGxlQXBwcm92ZUFsbENsaWNrIiwiaGFuZGxlQXBwcm92ZUFsbENvbmZpcm0iLCJ1c2VyRW1haWwiLCJ0b2tlbiIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJ1c2VyU3RyIiwiYXRvYiIsInNwbGl0IiwidXNlciIsInBhcnNlIiwiZW1haWwiLCJjb250ZW50IiwiYXBwcm92ZWRfYnkiLCJjb21tZW50Iiwib2siLCJzZXRUaW1lb3V0IiwiaGFuZGxlQXBwcm92ZUFsbENhbmNlbCIsImZvcm0iLCJvblN1Ym1pdCIsInByZXZlbnREZWZhdWx0IiwiaHRtbEZvciIsIm9uVmFsdWVDaGFuZ2UiLCJwbGFjZWhvbGRlciIsImMiLCJpZCIsInRvU3RyaW5nIiwiZG9tYWluIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/AiSummarizationPanel.tsx\n"));

/***/ })

});