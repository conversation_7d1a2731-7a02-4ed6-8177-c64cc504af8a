"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/ai-summarization/page",{

/***/ "(app-pages-browser)/./src/app/admin/AiSummarizationPanel.tsx":
/*!************************************************!*\
  !*** ./src/app/admin/AiSummarizationPanel.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AiSummarizationPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _barrel_optimize_names_Check_CheckCheck_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CheckCheck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_CheckCheck_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CheckCheck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-check.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction PageList(param) {\n    let { pages, selected, onSelect } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full mb-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \" font-semibold text-brand-blue/[85%] mt-2 mb-4\",\n                children: [\n                    \"Medical Course Pages (\",\n                    pages.length,\n                    \" pages)\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                className: \"h-full space-y-3 overflow-y-auto max-h-[calc(100vh-5%)] pr-2 custom-scrollbar\",\n                children: pages.map((p, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"w-full text-left px-5 py-3 rounded-xl border transition-all duration-200 flex flex-col gap-1 shadow-sm \\n                \".concat(selected === idx ? \"border-orange-500 bg-orange-50 text-orange-800 shadow-md\" : \"border-gray-200 bg-white hover:bg-orange-50 hover:border-orange-300\", \"\\n              \"),\n                            onClick: ()=>onSelect(idx),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-bold text-lg\",\n                                    children: [\n                                        \"Page \",\n                                        p.page\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600 truncate\",\n                                    children: p.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"inline-block mt-1 text-xs px-3 py-1 rounded-full font-medium \\n                \".concat(p.status === \"Approved\" ? \"bg-green-100 text-green-700\" : \"bg-orange-100 text-orange-600\", \"\\n              \"),\n                                    children: p.status\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 13\n                        }, this)\n                    }, p.page, false, {\n                        fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n_c = PageList;\nfunction SummaryPanel(param) {\n    let { page, onChangeSummary, onApprove, onRegenerate, onCustomPrompt, onViewOriginal, onApproveAll, approvedCount, totalCount, pages, selected, onSelect } = param;\n    const progressPercentage = totalCount > 0 ? approvedCount / totalCount * 100 : 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 flex flex-col bg-white rounded-2xl shadow-xl px-4 py-4 border border-orange-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-brand-blue flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-7 h-7 text-orange-500\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: \"2\",\n                                    d: \"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-brand-blue\",\n                                children: \"AI-Powered Medical Summarization\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-md font-medium text-brand-blue-300\",\n                        children: [\n                            \"Progress: \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-orange-600\",\n                                children: approvedCount\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 76\n                            }, this),\n                            \" of \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-brand-blue-300\",\n                                children: totalCount\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 136\n                            }, this),\n                            \" approved\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full bg-brand-blue-300/[20%] rounded-full h-2.5 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-green-500 h-2.5 rounded-full transition-all duration-500 ease-out\",\n                    style: {\n                        width: \"\".concat(progressPercentage, \"%\")\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-3 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-bold text-brand-blue-300\",\n                                            children: page.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 flex justify-end gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.Tooltip, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipTrigger, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-100 text-sm font-medium transition flex items-center gap-1 shadow-sm\",\n                                                                onClick: onRegenerate,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 text-gray-600\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"2\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M4 4v5h.582M20 20v-5h-.581\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                            lineNumber: 68,\n                                                                            columnNumber: 130\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M5.077 19A9 9 0 1 0 6.5 6.5L4 9.5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                            lineNumber: 68,\n                                                                            columnNumber: 168\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                    lineNumber: 68,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                lineNumber: 67,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                            lineNumber: 66,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipContent, {\n                                                            variant: \"brand\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"Regenerate\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                lineNumber: 72,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                            lineNumber: 71,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                    lineNumber: 65,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.Tooltip, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipTrigger, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-100 text-sm font-medium transition flex items-center gap-1 shadow-sm\",\n                                                                onClick: onCustomPrompt,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 text-gray-600\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"2\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M16.862 3.487a2.6 2.6 0 1 1 3.677 3.677L7.5 20.205l-4.5 1 1-4.5L16.862 3.487Z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                            lineNumber: 78,\n                                                                            columnNumber: 126\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M15 6.5 17.5 9\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                            lineNumber: 78,\n                                                                            columnNumber: 215\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                    lineNumber: 78,\n                                                                    columnNumber: 17\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                lineNumber: 77,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                            lineNumber: 76,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipContent, {\n                                                            variant: \"brand\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"Custom Prompt\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                lineNumber: 82,\n                                                                columnNumber: 20\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                            lineNumber: 81,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.Tooltip, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipTrigger, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-100 text-sm font-medium transition flex items-center gap-1 shadow-sm\",\n                                                                onClick: onViewOriginal,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 text-gray-600\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"2\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M1.5 12S5 5 12 5s10.5 7 10.5 7-3.5 7-10.5 7S1.5 12 1.5 12Z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                            lineNumber: 88,\n                                                                            columnNumber: 126\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                            cx: \"12\",\n                                                                            cy: \"12\",\n                                                                            r: \"3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                            lineNumber: 88,\n                                                                            columnNumber: 196\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                    lineNumber: 88,\n                                                                    columnNumber: 17\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                lineNumber: 87,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                            lineNumber: 86,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipContent, {\n                                                            variant: \"brand\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"View Original\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                lineNumber: 92,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                            lineNumber: 91,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                    lineNumber: 85,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 flex-1 flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"AI-Generated Summary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            className: \"w-full border border-gray-200 rounded-lg px-4 py-3 min-h-[150px] focus:ring-2 focus:ring-orange-200 focus:border-orange-300 transition-colors duration-200 resize-y flex-1\",\n                                            value: page.aiSummary,\n                                            onChange: (e)=>onChangeSummary(e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mt-4 text-xs text-gray-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-orange-50 text-orange-600 px-2 py-1 rounded-full font-medium\",\n                                                    children: \"Generated by Azure OpenAI GPT-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-brand-blue/[25%] text-brand-blue px-2 py-1 rounded-full font-medium\",\n                                                    children: \"Medical AI Model\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 flex-1 flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Original Content Preview\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 text-gray-700 text-sm min-h-[150px] overflow-y-auto flex-1 custom-scrollbar\",\n                                            children: page.original\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-3 mt-2 justify-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            variant: 'outline',\n                                            className: \"text-brand-blue border-brand-blue hover:bg-brand-blue hover:text-white px-6 py-3 rounded-lg font-semibold shadow-md hover:shadow-lg transition-all duration-200 flex items-center gap-2\",\n                                            onClick: onApprove,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCheck_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 15\n                                                }, this),\n                                                \"Approve\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            variant: 'outline',\n                                            className: \"text-brand-blue border-brand-blue hover:bg-brand-blue hover:text-white px-6 py-3 rounded-lg font-semibold shadow-md hover:shadow-lg transition-all duration-200 flex items-center gap-2\",\n                                            onClick: onApproveAll,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCheck_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 15\n                                                }, this),\n                                                \"Approve All (\",\n                                                pages.filter((p)=>p.status === \"Pending Review\").length,\n                                                \" remaining)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PageList, {\n                            pages: pages,\n                            selected: selected,\n                            onSelect: onSelect\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n_c1 = SummaryPanel;\nfunction AiSummarizationPanel() {\n    _s();\n    const [selected, setSelected] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(0);\n    const [pages, setPages] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [showPromptModal, setShowPromptModal] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [customPrompt, setCustomPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [courses, setCourses] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [selectedCourseId, setSelectedCourseId] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [showApproveAllModal, setShowApproveAllModal] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [selectedCourseBlobUrl, setSelectedCourseBlobUrl] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"AiSummarizationPanel.useEffect\": ()=>{\n            // Fetch courses on mount\n            fetch(\"http://172.24.175.70:5001\" + \"/api/courses\").then({\n                \"AiSummarizationPanel.useEffect\": (res)=>res.json()\n            }[\"AiSummarizationPanel.useEffect\"]).then({\n                \"AiSummarizationPanel.useEffect\": (data)=>setCourses(data)\n            }[\"AiSummarizationPanel.useEffect\"]);\n        }\n    }[\"AiSummarizationPanel.useEffect\"], []);\n    const handleCourseSelect = async (value)=>{\n        const courseId = value;\n        setSelectedCourseId(courseId);\n        setLoading(true);\n        const res = await fetch(\"http://172.24.175.70:5001\" + '/api/courses/' + courseId);\n        const data = await res.json();\n        // Map pdf_output to the format expected by the UI\n        const mappedPages = (data.pdf_output || []).map((p, idx)=>({\n                page: p.page || idx + 1,\n                title: \"Page \".concat(p.page || idx + 1),\n                status: \"Pending Review\",\n                aiSummary: p.imagetext || \"\",\n                original: p.imagetext || \"\",\n                imageassestid: p.imageassestid\n            }));\n        setPages(mappedPages);\n        setSelected(0);\n        setLoading(false);\n        setSelectedCourseBlobUrl(data.blob_url || null);\n    };\n    const handleChangeSummary = (val)=>{\n        setPages((pages)=>pages.map((p, idx)=>idx === selected ? {\n                    ...p,\n                    aiSummary: val\n                } : p));\n    };\n    const handleApprove = ()=>{\n        setPages((pages)=>pages.map((p, idx)=>idx === selected ? {\n                    ...p,\n                    status: \"Approved\"\n                } : p));\n    };\n    const handleApproveAll = ()=>{\n        setPages((pages)=>pages.map((p)=>({\n                    ...p,\n                    status: \"Approved\"\n                })));\n    };\n    const handleRegenerate = async ()=>{\n        const text = pages[selected].aiSummary;\n        try {\n            const res = await fetch(\"\".concat(\"http://172.24.175.70:5001\", \"/api/rephrase\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    text\n                })\n            });\n            const data = await res.json();\n            if (data && data.rephrased) {\n                setPages((pages)=>pages.map((p, idx)=>idx === selected ? {\n                            ...p,\n                            aiSummary: data.rephrased\n                        } : p));\n            } else if (typeof data === 'string') {\n                setPages((pages)=>pages.map((p, idx)=>idx === selected ? {\n                            ...p,\n                            aiSummary: data\n                        } : p));\n            }\n        } catch (err) {\n            alert('Failed to rephrase.');\n        }\n    };\n    const handleCustomPrompt = ()=>{\n        setShowPromptModal(true);\n    };\n    const handleViewOriginal = ()=>{\n        if (selectedCourseBlobUrl) {\n            window.open(selectedCourseBlobUrl, '_blank');\n        } else {\n            alert(\"No original content URL available.\");\n        }\n    };\n    const handlePromptCancel = ()=>{\n        setShowPromptModal(false);\n        setCustomPrompt(\"\");\n    };\n    const handlePromptGenerate = async ()=>{\n        const summaryText = pages[selected].aiSummary;\n        const promptText = customPrompt;\n        try {\n            const res = await fetch(\"\".concat(process.env.NEX, \"/api/customrephrase\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    text: summaryText,\n                    prompt: promptText\n                })\n            });\n            const data = await res.json();\n            if (data && data.result) {\n                setPages((pages)=>pages.map((p, idx)=>idx === selected ? {\n                            ...p,\n                            aiSummary: data.result\n                        } : p));\n            } else {\n                alert('Failed to get rephrased result.');\n            }\n        } catch (err) {\n            alert('Failed to get rephrased result.');\n        }\n        setShowPromptModal(false);\n        setCustomPrompt(\"\");\n    };\n    const handleApproveAllClick = ()=>{\n        setShowApproveAllModal(true);\n    };\n    const handleApproveAllConfirm = async ()=>{\n        setShowApproveAllModal(false);\n        handleApproveAll();\n        // Get user email from localStorage token\n        let userEmail = \"unknown\";\n        try {\n            const token = localStorage.getItem(\"token\");\n            if (token) {\n                const userStr = atob(token.split(\".\")[1]);\n                const user = JSON.parse(userStr);\n                userEmail = user.email || \"unknown\";\n            }\n        } catch (e) {}\n        // Send approved data to backend\n        try {\n            const res = await fetch(\"\".concat(\"http://172.24.175.70:5001\", \"/api/courses/\").concat(selectedCourseId, \"/approve\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    content: pages.map((p)=>({\n                            ...p,\n                            status: \"Approved\"\n                        })),\n                    approved_by: userEmail,\n                    comment: ''\n                })\n            });\n            if (!res.ok) {\n                alert('Failed to save approved content!');\n            } else {\n                setSuccessMessage('Content approved and saved!');\n                setTimeout(()=>setSuccessMessage(\"\"), 3000);\n            }\n        } catch (err) {\n            alert('Failed to save approved content!');\n        }\n    };\n    const handleApproveAllCancel = ()=>{\n        setShowApproveAllModal(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full px-4 py-4 flex flex-col items-stretch justify-start relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                className: \"flex gap-4 items-end py-4\",\n                onSubmit: (e)=>e.preventDefault(),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_2__.Label, {\n                            htmlFor: \"course\",\n                            className: \"py-2\",\n                            children: \"Course\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.Select, {\n                            value: selectedCourseId || '',\n                            onValueChange: (value)=>handleCourseSelect(value),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectTrigger, {\n                                    className: \"w-auto focus:ring-2 focus:ring-orange-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectValue, {\n                                        placeholder: \"Select Course\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectContent, {\n                                    children: courses.map((c)=>// <option key={c.id} value={c.id}>{c.title} ({c.domain})</option>\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                            value: c.id.toString(),\n                                            children: [\n                                                c.title,\n                                                \" (\",\n                                                c.domain,\n                                                \")\"\n                                            ]\n                                        }, c.id, true, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                    lineNumber: 296,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 295,\n                columnNumber: 9\n            }, this),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-lg text-orange-500\",\n                children: \"Loading course data...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 326,\n                columnNumber: 19\n            }, this),\n            !loading && pages.length > 0 && // <div className=\"w-full bg-white rounded-2xl shadow-lg p-8 mt-8\">\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SummaryPanel, {\n                page: pages[selected],\n                onChangeSummary: handleChangeSummary,\n                onApprove: handleApprove,\n                onRegenerate: handleRegenerate,\n                onCustomPrompt: handleCustomPrompt,\n                onViewOriginal: handleViewOriginal,\n                onApproveAll: handleApproveAllClick,\n                approvedCount: pages.filter((p)=>p.status === \"Approved\").length,\n                totalCount: pages.length,\n                pages: pages,\n                selected: selected,\n                onSelect: setSelected\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 329,\n                columnNumber: 11\n            }, this),\n            showPromptModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full animate-fade-in-up relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-bold text-gray-800 mb-4\",\n                            children: \"Custom AI Prompt\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            className: \"w-full border border-gray-300 rounded-lg p-3 min-h-[120px] focus:ring-2 focus:ring-orange-200 focus:border-orange-300 transition-colors duration-200 resize-y\",\n                            placeholder: \"Enter your custom prompt here to regenerate the summary...\",\n                            value: customPrompt,\n                            onChange: (e)=>setCustomPrompt(e.target.value)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-3 rounded-lg font-semibold transition-colors duration-200\",\n                                    onClick: handlePromptCancel,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200\",\n                                    onClick: handlePromptGenerate,\n                                    children: \"Generate\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 357,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                    lineNumber: 349,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 348,\n                columnNumber: 9\n            }, this),\n            showApproveAllModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-2xl p-8 max-w-sm w-full animate-fade-in-up relative text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-bold text-gray-800 mb-4\",\n                            children: \"Confirm Approval\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-6\",\n                            children: \"Are you sure you want to approve all pending summaries for this course? This action cannot be undone.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 380,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-3 rounded-lg font-semibold transition-colors duration-200\",\n                                    onClick: handleApproveAllCancel,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200\",\n                                    onClick: handleApproveAllConfirm,\n                                    children: \"Confirm Approve All\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 381,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                    lineNumber: 378,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 377,\n                columnNumber: 9\n            }, this),\n            successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-6 right-6 bg-green-500 text-white px-6 py-3 rounded-lg shadow-xl text-lg font-semibold animate-fade-in-right z-50\",\n                children: successMessage\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 400,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n        lineNumber: 292,\n        columnNumber: 5\n    }, this);\n}\n_s(AiSummarizationPanel, \"CO/z/i0KmRkbIZmMS/BiU85F0sQ=\");\n_c2 = AiSummarizationPanel;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"PageList\");\n$RefreshReg$(_c1, \"SummaryPanel\");\n$RefreshReg$(_c2, \"AiSummarizationPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/AiSummarizationPanel.tsx\n"));

/***/ })

});