"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/ai-summarization/page",{

/***/ "(app-pages-browser)/./src/app/admin/AiSummarizationPanel.tsx":
/*!************************************************!*\
  !*** ./src/app/admin/AiSummarizationPanel.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AiSummarizationPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _barrel_optimize_names_Check_CheckCheck_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CheckCheck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_CheckCheck_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CheckCheck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-check.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction PageList(param) {\n    let { pages, selected, onSelect } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full mb-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \" font-semibold text-brand-blue/[85%] mt-2 mb-4\",\n                children: [\n                    \"Medical Course Pages (\",\n                    pages.length,\n                    \" pages)\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                className: \"h-full space-y-3 overflow-y-auto max-h-[calc(100vh-5%)] pr-2 custom-scrollbar\",\n                children: pages.map((p, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"w-full text-left px-5 py-3 rounded-xl border transition-all duration-200 flex flex-col gap-1 shadow-sm \\n                \".concat(selected === idx ? \"border-orange-500 bg-orange-50 text-orange-800 shadow-md\" : \"border-gray-200 bg-white hover:bg-orange-50 hover:border-orange-300\", \"\\n              \"),\n                            onClick: ()=>onSelect(idx),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-bold text-lg\",\n                                    children: [\n                                        \"Page \",\n                                        p.page\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600 truncate\",\n                                    children: p.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"inline-block mt-1 text-xs px-3 py-1 rounded-full font-medium \\n                \".concat(p.status === \"Approved\" ? \"bg-green-100 text-green-700\" : \"bg-orange-100 text-orange-600\", \"\\n              \"),\n                                    children: p.status\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 13\n                        }, this)\n                    }, p.page, false, {\n                        fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n_c = PageList;\nfunction SummaryPanel(param) {\n    let { page, onChangeSummary, onApprove, onRegenerate, onCustomPrompt, onViewOriginal, onApproveAll, approvedCount, totalCount, pages, selected, onSelect } = param;\n    const progressPercentage = totalCount > 0 ? approvedCount / totalCount * 100 : 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 flex flex-col bg-white rounded-2xl shadow-xl px-4 py-4 border border-orange-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-brand-blue flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-7 h-7 text-orange-500\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: \"2\",\n                                    d: \"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-brand-blue\",\n                                children: \"AI-Powered Medical Summarization\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-md font-medium text-brand-blue-300\",\n                        children: [\n                            \"Progress: \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-orange-600\",\n                                children: approvedCount\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 76\n                            }, this),\n                            \" of \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-brand-blue-300\",\n                                children: totalCount\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 136\n                            }, this),\n                            \" approved\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full bg-brand-blue-300/[20%] rounded-full h-2.5 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-green-500 h-2.5 rounded-full transition-all duration-500 ease-out\",\n                    style: {\n                        width: \"\".concat(progressPercentage, \"%\")\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-3 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-bold text-brand-blue-300\",\n                                            children: page.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 flex justify-end gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.Tooltip, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipTrigger, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-100 text-sm font-medium transition flex items-center gap-1 shadow-sm\",\n                                                                onClick: onRegenerate,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 text-gray-600\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"2\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M4 4v5h.582M20 20v-5h-.581\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                            lineNumber: 68,\n                                                                            columnNumber: 130\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M5.077 19A9 9 0 1 0 6.5 6.5L4 9.5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                            lineNumber: 68,\n                                                                            columnNumber: 168\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                    lineNumber: 68,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                lineNumber: 67,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                            lineNumber: 66,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipContent, {\n                                                            variant: \"brand\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"Regenerate\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                lineNumber: 72,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                            lineNumber: 71,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                    lineNumber: 65,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.Tooltip, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipTrigger, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-100 text-sm font-medium transition flex items-center gap-1 shadow-sm\",\n                                                                onClick: onCustomPrompt,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 text-gray-600\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"2\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M16.862 3.487a2.6 2.6 0 1 1 3.677 3.677L7.5 20.205l-4.5 1 1-4.5L16.862 3.487Z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                            lineNumber: 78,\n                                                                            columnNumber: 126\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M15 6.5 17.5 9\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                            lineNumber: 78,\n                                                                            columnNumber: 215\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                    lineNumber: 78,\n                                                                    columnNumber: 17\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                lineNumber: 77,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                            lineNumber: 76,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipContent, {\n                                                            variant: \"brand\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"Custom Prompt\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                lineNumber: 82,\n                                                                columnNumber: 20\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                            lineNumber: 81,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.Tooltip, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipTrigger, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-100 text-sm font-medium transition flex items-center gap-1 shadow-sm\",\n                                                                onClick: onViewOriginal,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 text-gray-600\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"2\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M1.5 12S5 5 12 5s10.5 7 10.5 7-3.5 7-10.5 7S1.5 12 1.5 12Z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                            lineNumber: 88,\n                                                                            columnNumber: 126\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                            cx: \"12\",\n                                                                            cy: \"12\",\n                                                                            r: \"3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                            lineNumber: 88,\n                                                                            columnNumber: 196\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                    lineNumber: 88,\n                                                                    columnNumber: 17\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                lineNumber: 87,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                            lineNumber: 86,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipContent, {\n                                                            variant: \"brand\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"View Original\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                                lineNumber: 92,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                            lineNumber: 91,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                    lineNumber: 85,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 flex-1 flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"AI-Generated Summary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            className: \"w-full border border-gray-200 rounded-lg px-4 py-3 min-h-[150px] focus:ring-2 focus:ring-orange-200 focus:border-orange-300 transition-colors duration-200 resize-y flex-1\",\n                                            value: page.aiSummary,\n                                            onChange: (e)=>onChangeSummary(e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mt-4 text-xs text-gray-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-orange-50 text-orange-600 px-2 py-1 rounded-full font-medium\",\n                                                    children: \"Generated by Azure OpenAI GPT-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-brand-blue/[25%] text-brand-blue px-2 py-1 rounded-full font-medium\",\n                                                    children: \"Medical AI Model\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 flex-1 flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Original Content Preview\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 text-gray-700 text-sm min-h-[150px] overflow-y-auto flex-1 custom-scrollbar\",\n                                            children: page.original\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-3 mt-2 justify-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            variant: 'outline',\n                                            className: \"text-brand-blue border-brand-blue hover:bg-brand-blue hover:text-white px-6 py-3 rounded-lg font-semibold shadow-md hover:shadow-lg transition-all duration-200 flex items-center gap-2\",\n                                            onClick: onApprove,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCheck_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 15\n                                                }, this),\n                                                \"Approve\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            variant: 'outline',\n                                            className: \"text-brand-blue border-brand-blue hover:bg-brand-blue hover:text-white px-6 py-3 rounded-lg font-semibold shadow-md hover:shadow-lg transition-all duration-200 flex items-center gap-2\",\n                                            onClick: onApproveAll,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCheck_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 15\n                                                }, this),\n                                                \"Approve All (\",\n                                                pages.filter((p)=>p.status === \"Pending Review\").length,\n                                                \" remaining)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PageList, {\n                            pages: pages,\n                            selected: selected,\n                            onSelect: onSelect\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n_c1 = SummaryPanel;\nfunction AiSummarizationPanel() {\n    _s();\n    const [selected, setSelected] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(0);\n    const [pages, setPages] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [showPromptModal, setShowPromptModal] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [customPrompt, setCustomPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [courses, setCourses] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [selectedCourseId, setSelectedCourseId] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [showApproveAllModal, setShowApproveAllModal] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [selectedCourseBlobUrl, setSelectedCourseBlobUrl] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"AiSummarizationPanel.useEffect\": ()=>{\n            // Fetch courses on mount\n            fetch(\"http://172.24.175.70:5001\" + \"/api/courses\").then({\n                \"AiSummarizationPanel.useEffect\": (res)=>res.json()\n            }[\"AiSummarizationPanel.useEffect\"]).then({\n                \"AiSummarizationPanel.useEffect\": (data)=>setCourses(data)\n            }[\"AiSummarizationPanel.useEffect\"]);\n        }\n    }[\"AiSummarizationPanel.useEffect\"], []);\n    const handleCourseSelect = async (value)=>{\n        const courseId = value;\n        setSelectedCourseId(courseId);\n        setLoading(true);\n        const res = await fetch(\"http://172.24.175.70:5001\" + '/api/courses/' + courseId);\n        const data = await res.json();\n        // Map pdf_output to the format expected by the UI\n        const mappedPages = (data.pdf_output || []).map((p, idx)=>({\n                page: p.page || idx + 1,\n                title: \"Page \".concat(p.page || idx + 1),\n                status: \"Pending Review\",\n                aiSummary: p.imagetext || \"\",\n                original: p.imagetext || \"\",\n                imageassestid: p.imageassestid\n            }));\n        setPages(mappedPages);\n        setSelected(0);\n        setLoading(false);\n        setSelectedCourseBlobUrl(data.blob_url || null);\n    };\n    const handleChangeSummary = (val)=>{\n        setPages((pages)=>pages.map((p, idx)=>idx === selected ? {\n                    ...p,\n                    aiSummary: val\n                } : p));\n    };\n    const handleApprove = ()=>{\n        setPages((pages)=>pages.map((p, idx)=>idx === selected ? {\n                    ...p,\n                    status: \"Approved\"\n                } : p));\n    };\n    const handleApproveAll = ()=>{\n        setPages((pages)=>pages.map((p)=>({\n                    ...p,\n                    status: \"Approved\"\n                })));\n    };\n    const handleRegenerate = async ()=>{\n        const text = pages[selected].aiSummary;\n        try {\n            const res = await fetch(\"http://172.24.175.70:5002/rephrase?text=\".concat(encodeURIComponent(text)));\n            const data = await res.json();\n            if (data && data.rephrased) {\n                setPages((pages)=>pages.map((p, idx)=>idx === selected ? {\n                            ...p,\n                            aiSummary: data.rephrased\n                        } : p));\n            } else if (typeof data === 'string') {\n                setPages((pages)=>pages.map((p, idx)=>idx === selected ? {\n                            ...p,\n                            aiSummary: data\n                        } : p));\n            }\n        } catch (err) {\n            alert('Failed to rephrase.');\n        }\n    };\n    const handleCustomPrompt = ()=>{\n        setShowPromptModal(true);\n    };\n    const handleViewOriginal = ()=>{\n        if (selectedCourseBlobUrl) {\n            window.open(selectedCourseBlobUrl, '_blank');\n        } else {\n            alert(\"No original content URL available.\");\n        }\n    };\n    const handlePromptCancel = ()=>{\n        setShowPromptModal(false);\n        setCustomPrompt(\"\");\n    };\n    const handlePromptGenerate = async ()=>{\n        const summaryText = pages[selected].aiSummary;\n        const promptText = customPrompt;\n        try {\n            const res = await fetch(\"http://172.24.175.70:5002/customrephrase?text=\".concat(encodeURIComponent(summaryText), \"&prompt=\").concat(encodeURIComponent(promptText)));\n            const data = await res.json();\n            if (data && data.result) {\n                setPages((pages)=>pages.map((p, idx)=>idx === selected ? {\n                            ...p,\n                            aiSummary: data.result\n                        } : p));\n            } else {\n                alert('Failed to get rephrased result.');\n            }\n        } catch (err) {\n            alert('Failed to get rephrased result.');\n        }\n        setShowPromptModal(false);\n        setCustomPrompt(\"\");\n    };\n    const handleApproveAllClick = ()=>{\n        setShowApproveAllModal(true);\n    };\n    const handleApproveAllConfirm = async ()=>{\n        setShowApproveAllModal(false);\n        handleApproveAll();\n        // Get user email from localStorage token\n        let userEmail = \"unknown\";\n        try {\n            const token = localStorage.getItem(\"token\");\n            if (token) {\n                const userStr = atob(token.split(\".\")[1]);\n                const user = JSON.parse(userStr);\n                userEmail = user.email || \"unknown\";\n            }\n        } catch (e) {}\n        // Send approved data to backend\n        try {\n            const res = await fetch(\"\".concat(\"http://172.24.175.70:5001\", \"/api/courses/\").concat(selectedCourseId, \"/approve\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    content: pages.map((p)=>({\n                            ...p,\n                            status: \"Approved\"\n                        })),\n                    approved_by: userEmail,\n                    comment: ''\n                })\n            });\n            if (!res.ok) {\n                alert('Failed to save approved content!');\n            } else {\n                setSuccessMessage('Content approved and saved!');\n                setTimeout(()=>setSuccessMessage(\"\"), 3000);\n            }\n        } catch (err) {\n            alert('Failed to save approved content!');\n        }\n    };\n    const handleApproveAllCancel = ()=>{\n        setShowApproveAllModal(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full px-4 py-4 flex flex-col items-stretch justify-start relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                className: \"flex gap-4 items-end py-4\",\n                onSubmit: (e)=>e.preventDefault(),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_2__.Label, {\n                            htmlFor: \"course\",\n                            className: \"py-2\",\n                            children: \"Course\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.Select, {\n                            value: selectedCourseId || '',\n                            onValueChange: (value)=>handleCourseSelect(value),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectTrigger, {\n                                    className: \"w-auto focus:ring-2 focus:ring-orange-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectValue, {\n                                        placeholder: \"Select Course\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectContent, {\n                                    children: courses.map((c)=>// <option key={c.id} value={c.id}>{c.title} ({c.domain})</option>\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                            value: c.id.toString(),\n                                            children: [\n                                                c.title,\n                                                \" (\",\n                                                c.domain,\n                                                \")\"\n                                            ]\n                                        }, c.id, true, {\n                                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 283,\n                columnNumber: 9\n            }, this),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-lg text-orange-500\",\n                children: \"Loading course data...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 314,\n                columnNumber: 19\n            }, this),\n            !loading && pages.length > 0 && // <div className=\"w-full bg-white rounded-2xl shadow-lg p-8 mt-8\">\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SummaryPanel, {\n                page: pages[selected],\n                onChangeSummary: handleChangeSummary,\n                onApprove: handleApprove,\n                onRegenerate: handleRegenerate,\n                onCustomPrompt: handleCustomPrompt,\n                onViewOriginal: handleViewOriginal,\n                onApproveAll: handleApproveAllClick,\n                approvedCount: pages.filter((p)=>p.status === \"Approved\").length,\n                totalCount: pages.length,\n                pages: pages,\n                selected: selected,\n                onSelect: setSelected\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 317,\n                columnNumber: 11\n            }, this),\n            showPromptModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full animate-fade-in-up relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-bold text-gray-800 mb-4\",\n                            children: \"Custom AI Prompt\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            className: \"w-full border border-gray-300 rounded-lg p-3 min-h-[120px] focus:ring-2 focus:ring-orange-200 focus:border-orange-300 transition-colors duration-200 resize-y\",\n                            placeholder: \"Enter your custom prompt here to regenerate the summary...\",\n                            value: customPrompt,\n                            onChange: (e)=>setCustomPrompt(e.target.value)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-3 rounded-lg font-semibold transition-colors duration-200\",\n                                    onClick: handlePromptCancel,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200\",\n                                    onClick: handlePromptGenerate,\n                                    children: \"Generate\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                    lineNumber: 337,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 336,\n                columnNumber: 9\n            }, this),\n            showApproveAllModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-2xl p-8 max-w-sm w-full animate-fade-in-up relative text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-bold text-gray-800 mb-4\",\n                            children: \"Confirm Approval\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-6\",\n                            children: \"Are you sure you want to approve all pending summaries for this course? This action cannot be undone.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 368,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-3 rounded-lg font-semibold transition-colors duration-200\",\n                                    onClick: handleApproveAllCancel,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200\",\n                                    onClick: handleApproveAllConfirm,\n                                    children: \"Confirm Approve All\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                            lineNumber: 369,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                    lineNumber: 366,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 365,\n                columnNumber: 9\n            }, this),\n            successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-6 right-6 bg-green-500 text-white px-6 py-3 rounded-lg shadow-xl text-lg font-semibold animate-fade-in-right z-50\",\n                children: successMessage\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n                lineNumber: 388,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\AI-Trainer2\\\\frontend\\\\src\\\\app\\\\admin\\\\AiSummarizationPanel.tsx\",\n        lineNumber: 280,\n        columnNumber: 5\n    }, this);\n}\n_s(AiSummarizationPanel, \"CO/z/i0KmRkbIZmMS/BiU85F0sQ=\");\n_c2 = AiSummarizationPanel;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"PageList\");\n$RefreshReg$(_c1, \"SummaryPanel\");\n$RefreshReg$(_c2, \"AiSummarizationPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/AiSummarizationPanel.tsx\n"));

/***/ })

});